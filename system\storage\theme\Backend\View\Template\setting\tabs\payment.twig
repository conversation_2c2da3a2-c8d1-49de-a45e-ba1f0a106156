

<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Настройки за плащания</h1>
    <p class="text-gray-600 mt-1">Управлявайте методите за плащане и валутите в магазина</p>
</div>

<form id="payment-settings-form" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Payment Methods -->
        <div class="lg:col-span-2">
            <!-- Payment Methods List -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-bank-card-line mr-2"></i>
                    Методи за плащане
                </h2>
                
                <div id="payment-methods-list" class="space-y-4">
                    {% for method_code, method in payment_methods %}
                    <div data-method="{{ method_code }}" 
                         class="border border-gray-200 rounded p-4 {{ method.status ? 'bg-green-50' : 'opacity-50' }} hover:shadow-sm transition-all cursor-move">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="drag-handle text-gray-400 hover:text-gray-600 cursor-move">
                                    <i class="ri-drag-move-line text-lg"></i>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i class="{{ method.icon }} text-2xl text-gray-600"></i>
                                    <div>
                                        <h3 class="font-medium text-gray-900">{{ method.name }}</h3>
                                        <p class="text-sm text-gray-500">{{ method.description }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-4">
                                <!-- Commission Settings -->
                                <div class="flex items-center space-x-2">
                                    <select name="payment_methods[{{ method_code }}][commission_type]" 
                                            data-method="{{ method_code }}"
                                            class="commission-type-select text-sm border border-gray-300 rounded px-2 py-1">
                                        {% for type_code, type_name in commission_types %}
                                        <option value="{{ type_code }}" {{ (method.commission_type ?? 'fixed') == type_code ? 'selected' : '' }}>
                                            {{ type_name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    
                                    <input type="number" 
                                           name="payment_methods[{{ method_code }}][commission_value]"
                                           id="commission_value_{{ method_code }}"
                                           class="text-sm border border-gray-300 rounded px-2 py-1 w-20"
                                           value="{{ method.commission_value ?? 0 }}"
                                           step="0.01"
                                           min="0"
                                           placeholder="0.00">
                                    
                                    <input type="number" 
                                           name="payment_methods[{{ method_code }}][sort_order]"
                                           class="text-sm border border-gray-300 rounded px-2 py-1 w-16"
                                           value="{{ method.sort_order ?? 0 }}"
                                           min="0"
                                           placeholder="0">
                                </div>
                                
                                <!-- Test Button -->
                                <button type="button" 
                                        data-method="{{ method_code }}"
                                        class="test-payment-method px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors">
                                    <i class="ri-test-tube-line mr-1"></i>
                                    Тест
                                </button>
                                
                                <!-- Status Toggle -->
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" 
                                           name="payment_methods[{{ method_code }}][status]"
                                           data-method="{{ method_code }}"
                                           class="payment-method-toggle toggle-switch"
                                           {{ method.status ? 'checked' : '' }}>
                                    <span class="ml-2 text-sm text-gray-700">Активен</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Advanced Settings (Collapsible) -->
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">
                                        Минимална сума (лв.)
                                    </label>
                                    <input type="number" 
                                           name="payment_methods[{{ method_code }}][minimum_amount]"
                                           class="w-full text-sm border border-gray-300 rounded px-2 py-1"
                                           value="{{ method.minimum_amount ?? 0 }}"
                                           step="0.01"
                                           min="0"
                                           placeholder="0.00">
                                </div>
                                
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">
                                        Максимална сума (лв.)
                                    </label>
                                    <input type="number" 
                                           name="payment_methods[{{ method_code }}][maximum_amount]"
                                           class="w-full text-sm border border-gray-300 rounded px-2 py-1"
                                           value="{{ method.maximum_amount ?? 0 }}"
                                           step="0.01"
                                           min="0"
                                           placeholder="0.00">
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="mt-4 text-sm text-gray-500">
                    <i class="ri-information-line mr-1"></i>
                    Плъзнете и пуснете за да промените подредбата на методите за плащане
                </div>
            </div>

            <!-- Currency Settings -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-money-dollar-circle-line mr-2"></i>
                    Валути и курсове
                </h2>
                
                <div class="space-y-4">
                    <div>
                        <label for="default_currency" class="block text-sm font-medium text-gray-700 mb-1">
                            Основна валута
                        </label>
                        <select id="default_currency" 
                                name="default_currency"
                                class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                            {% for currency in currencies %}
                            <option value="{{ currency.code }}" {{ default_currency == currency.code ? 'selected' : '' }}>
                                {{ currency.title }} ({{ currency.symbol_left }}{{ currency.symbol_right }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               id="payment_auto_currency_update"
                               name="payment_auto_currency_update"
                               class="toggle-switch"
                               {{ payment_auto_currency_update ? 'checked' : '' }}>
                        <div>
                            <label for="payment_auto_currency_update" class="text-sm font-medium text-gray-700">
                                Автоматично актуализиране на курсове
                            </label>
                            <p class="text-xs text-gray-500">Актуализира валутните курсове автоматично</p>
                        </div>
                    </div>

                    <div>
                        <label for="payment_currency_update_frequency" class="block text-sm font-medium text-gray-700 mb-1">
                            Честота на актуализиране
                        </label>
                        <select id="payment_currency_update_frequency" 
                                name="payment_currency_update_frequency"
                                class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                            {% for value, label in currency_update_options %}
                            <option value="{{ value }}" {{ payment_currency_update_frequency == value ? 'selected' : '' }}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- Currency Rates Table -->
                <div class="mt-6">
                    <h3 class="text-md font-medium text-gray-800 mb-3">Текущи курсове</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Валута</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Курс</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Последна актуализация</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Статус</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for currency in currencies %}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="font-medium text-gray-900">{{ currency.code }}</span>
                                            <span class="ml-2 text-sm text-gray-500">{{ currency.title }}</span>
                                        </div>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span id="currency-rate-{{ currency.code }}" class="text-sm text-gray-900">
                                            {{ currency.value }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span id="currency-update-{{ currency.code }}" class="text-sm text-gray-500">
                                            {{ currency.date_modified ?? 'Никога' }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ currency.status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ currency.status ? 'Активна' : 'Неактивна' }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- General Payment Settings -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-settings-3-line mr-2"></i>
                    Общи настройки за плащания
                </h2>
                
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="payment_minimum_order" class="block text-sm font-medium text-gray-700 mb-1">
                                Минимална сума за поръчка (лв.)
                            </label>
                            <input type="number" 
                                   id="payment_minimum_order" 
                                   name="payment_minimum_order"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ payment_minimum_order ?? 0 }}"
                                   step="0.01"
                                   min="0"
                                   placeholder="0.00">
                        </div>

                        <div>
                            <label for="payment_maximum_order" class="block text-sm font-medium text-gray-700 mb-1">
                                Максимална сума за поръчка (лв.)
                            </label>
                            <input type="number" 
                                   id="payment_maximum_order" 
                                   name="payment_maximum_order"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ payment_maximum_order ?? 0 }}"
                                   step="0.01"
                                   min="0"
                                   placeholder="0.00">
                            <p class="text-xs text-gray-500 mt-1">0 = без ограничение</p>
                        </div>
                    </div>

                    <div>
                        <label for="payment_terms" class="block text-sm font-medium text-gray-700 mb-1">
                            Условия за плащане
                        </label>
                        <textarea id="payment_terms" 
                                  name="payment_terms"
                                  rows="3" 
                                  class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                  maxlength="1000"
                                  placeholder="Въведете условията за плащане...">{{ payment_terms ?? '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Максимум 1000 символа</p>
                    </div>

                    <div>
                        <label for="payment_privacy_policy" class="block text-sm font-medium text-gray-700 mb-1">
                            Политика за поверителност при плащания
                        </label>
                        <textarea id="payment_privacy_policy" 
                                  name="payment_privacy_policy"
                                  rows="3" 
                                  class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                  maxlength="1000"
                                  placeholder="Въведете политиката за поверителност...">{{ payment_privacy_policy ?? '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Максимум 1000 символа</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Panel -->
        <div class="lg:col-span-1">
            <!-- Payment Statistics -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-bar-chart-line mr-2"></i>
                    Статистики за плащания
                </h2>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Активни методи:</span>
                        <span id="active-payment-methods-count" class="text-sm font-medium text-green-600">
                            {{ active_payment_methods ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Общо методи:</span>
                        <span class="text-sm font-medium text-blue-600">
                            {{ total_payment_methods ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Поддържани валути:</span>
                        <span class="text-sm font-medium text-purple-600">
                            {{ supported_currencies ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Последна актуализация:</span>
                        <span class="text-sm font-medium text-gray-600">
                            {{ last_currency_update ?? 'Никога' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-tools-line mr-2"></i>
                    Бързи действия
                </h2>
                
                <div class="space-y-3">
                    <button type="button" 
                            id="save-payment-settings"
                            class="w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors">
                        <i class="ri-save-line mr-2"></i>
                        Запази настройки
                    </button>



                    <button type="button" 
                            onclick="location.reload()"
                            class="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        <i class="ri-refresh-line mr-2"></i>
                        Презареди
                    </button>
                </div>
            </div>

            <!-- Payment Tips -->
            <div class="bg-blue-50 border border-blue-200 rounded p-6 mt-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="ri-lightbulb-line mr-2"></i>
                    Съвети за плащания
                </h2>
                
                <div class="space-y-3 text-sm text-blue-700">
                    <div class="flex items-start">
                        <i class="ri-check-line text-blue-500 mr-2 mt-0.5"></i>
                        <span>Активирайте поне два метода за плащане за по-добро потребителско изживяване</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-blue-500 mr-2 mt-0.5"></i>
                        <span>Редовно актуализирайте валутните курсове за точни цени</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-blue-500 mr-2 mt-0.5"></i>
                        <span>Тествайте payment методите преди да ги активирате</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-blue-500 mr-2 mt-0.5"></i>
                        <span>Задайте разумни комисионни за покриване на разходите</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

{% if tab_script_url %}
<script type="text/javascript" src="{{ tab_script_url }}"></script>
{% endif %}
