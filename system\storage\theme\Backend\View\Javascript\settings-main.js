/**
 * JavaScript модул за настройки - Основен файл
 * Следва BackendModule pattern на темата Rakla.bg
 * Съдържа общи функционалности за всички табове
 */
(function() {
    'use strict';

    // Функция за инициализация на settings модула
    function initSettingsModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModule();
            // Инициализация на settings
            BackendModule.initSettings();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул
    function extendBackendModule() {
        Object.assign(BackendModule, {
            // Конфигурация за настройки
            settings: {
                currentTab: 'basic',
                modules: {},
                autoSaveTimeout: null,
                config: {
                    userToken: new URLSearchParams(window.location.search).get('user_token') || '',
                    baseUrl: '',
                    ajaxUrls: {}
                }
            },

            /**
             * Инициализация на модула за настройки
             */
            initSettings: function() {
                this.loadSettingsConfig();
                this.bindSettingsEvents();
                this.initSettingsComponents();
                this.initGlobalModalEvents();
                this.logDev && this.logDev('Settings модул инициализиран');
            },

            /**
             * Зареждане на конфигурация за настройки
             */
            loadSettingsConfig: function() {
                // Зареждане на конфигурация от глобални променливи
                if (window.settingsConfig) {
                    this.settings.config = { ...this.settings.config, ...window.settingsConfig };
                }

                // Извличане на user_token от URL
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('user_token')) {
                    this.settings.config.userToken = urlParams.get('user_token');
                }

                // Определяне на текущия таб
                this.settings.currentTab = urlParams.get('tab') || 'basic';
            },

            /**
             * Свързване на събития за настройки
             */
            bindSettingsEvents: function() {
                // Tab навигация
                this.bindSettingsTabEvents();

                // Форми за настройки
                this.bindSettingsFormEvents();

                // Известия
                this.initSettingsNotifications();
            },

            /**
             * Свързване на събития за табове
             */
            bindSettingsTabEvents: function() {
                const self = this;
                const tabButtons = document.querySelectorAll('.tab-button');
                const tabContents = document.querySelectorAll('.tab-content');

                this.logDev && this.logDev('bindSettingsTabEvents called');
                this.logDev && this.logDev('Found tab buttons: ' + tabButtons.length);
                this.logDev && this.logDev('Found tab contents: ' + tabContents.length);

                // Initialize tab cache and form data storage
                if (!this.settings.tabCache) {
                    this.settings.tabCache = new Map();
                }
                if (!this.settings.tabFormData) {
                    this.settings.tabFormData = new Map();
                }

                tabButtons.forEach((button, index) => {
                    self.logDev && self.logDev('Adding event listener to button ' + index + ': ' + button.id);
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const tabId = this.getAttribute('data-tab') || this.id.replace('tab-', '');
                        self.logDev && self.logDev('Tab button clicked: ' + tabId);

                        // Don't reload if clicking on active tab
                        if (this.classList.contains('active')) {
                            self.logDev && self.logDev('Tab already active, skipping: ' + tabId);
                            return;
                        }

                        self.switchSettingsTab(tabId);
                    });
                });

                // Auto-save functionality to prevent data loss
                if (!this.settings.autoSaveTimeout) {
                    document.addEventListener('input', function(e) {
                        if (e.target.closest('.tab-content')) {
                            clearTimeout(self.settings.autoSaveTimeout);
                            self.settings.autoSaveTimeout = setTimeout(() => {
                                self.saveCurrentTabFormData();
                            }, 1000); // Save after 1 second of inactivity
                        }
                    });
                }

                // Initialize the current tab
                const currentTab = this.settings.config.currentTab || this.settings.currentTab || 'basic';
                this.logDev && this.logDev('Initializing tab: ' + currentTab);
                this.switchSettingsTab(currentTab);
            },

            /**
             * Превключване на таб в настройките
             */
            switchSettingsTab: function(tabId) {
                this.logDev && this.logDev('Switching to tab: ' + tabId);

                // Save current form data before switching
                this.saveCurrentTabFormData();

                // Update current tab
                this.settings.currentTab = tabId;

                // Update visual state of tab buttons
                const tabButtons = document.querySelectorAll('.tab-button');
                this.logDev && this.logDev('Found tab buttons: ' + tabButtons.length);

                tabButtons.forEach(btn => {
                    const btnTabId = btn.getAttribute('data-tab') || btn.id.replace('tab-', '');
                    if (btnTabId === tabId) {
                        btn.classList.add('active', 'text-primary');
                        btn.classList.remove('text-gray-600');
                        this.logDev && this.logDev('Activated tab button: ' + btnTabId);
                    } else {
                        btn.classList.remove('active', 'text-primary');
                        btn.classList.add('text-gray-600');
                    }
                });

                // Hide all tab contents
                const tabContents = document.querySelectorAll('.tab-content');
                this.logDev && this.logDev('Found tab contents: ' + tabContents.length);

                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });

                // Show target tab content
                const targetContent = document.getElementById('content-' + tabId);
                if (targetContent) {
                    targetContent.classList.remove('hidden');
                    this.logDev && this.logDev('Showed content for tab: ' + tabId);

                    // Load content via AJAX if needed
                    this.loadTabContent(tabId, targetContent);
                } else {
                    this.logDev && this.logDev('Content element not found for tab: ' + tabId);
                }

                // Update URL without reload
                this.updateSettingsUrl(tabId);

                this.logDev && this.logDev('Settings tab switched to: ' + tabId);
            },

            /**
             * Зареждане на модул за конкретен таб
             */
            loadSettingsModule: function(tabName) {
                // Проверка дали модулът вече е зареден
                if (this.settings.modules[tabName]) {
                    // Ако модулът има метод за активиране, го извикваме
                    if (typeof this.settings.modules[tabName].activate === 'function') {
                        this.settings.modules[tabName].activate();
                    }
                    this.logDev && this.logDev('Settings module already loaded: ' + tabName);
                    return;
                }

                // Опит за намиране на модула в BackendModule
                var parts = tabName.split('_');
                var moduleMethodName = 'init' + parts.map(function(part) {
                    return part.charAt(0).toUpperCase() + part.slice(1);
                }).join('') + 'Settings';

                if (typeof this[moduleMethodName] === 'function') {
                    try {
                        this.settings.modules[tabName] = { init: this[moduleMethodName].bind(this) };
                        this.settings.modules[tabName].init();
                        this.logDev && this.logDev('Settings module loaded successfully: ' + tabName + ' (' + moduleMethodName + ')');
                    } catch (error) {
                        this.logDev && this.logDev('Error initializing settings module: ' + tabName, error);
                    }
                } else {
                    this.logDev && this.logDev('Settings module method not found: ' + moduleMethodName + ' for tab: ' + tabName);
                    this.logDev && this.logDev('Available methods: ' + Object.getOwnPropertyNames(this).filter(name => name.startsWith('init') && name.endsWith('Settings')).join(', '));
                }
            },

            /**
             * Запазване на данни от текущия таб
             */
            saveCurrentTabFormData: function() {
                const activeTab = document.querySelector('.tab-button.active');
                if (activeTab) {
                    const tabId = activeTab.getAttribute('data-tab');
                    const activeContent = document.getElementById('content-' + tabId);
                    if (activeContent) {
                        const forms = activeContent.querySelectorAll('form');
                        forms.forEach(form => {
                            const formData = new FormData(form);
                            const formDataObj = {};
                            for (let [key, value] of formData.entries()) {
                                formDataObj[key] = value;
                            }
                            this.settings.tabFormData.set(tabId, formDataObj);
                        });
                    }
                }
            },

            /**
             * Възстановяване на данни в таб
             */
            restoreTabFormData: function(tabId, content) {
                const savedData = this.settings.tabFormData.get(tabId);
                if (savedData) {
                    const forms = content.querySelectorAll('form');
                    forms.forEach(form => {
                        Object.keys(savedData).forEach(key => {
                            const field = form.querySelector(`[name="${key}"]`);
                            if (field) {
                                if (field.type === 'checkbox' || field.type === 'radio') {
                                    field.checked = savedData[key] === field.value;
                                } else if (field.type === 'file') {
                                    // File input полета не могат да бъдат задавани програмно по security причини
                                    // Пропускаме ги при възстановяване на данни
                                    this.logDev && this.logDev('Skipping file input field: ' + key);
                                } else {
                                    field.value = savedData[key];
                                }
                            }
                        });
                    });
                }
            },

            /**
             * Зареждане на съдържание на таб чрез AJAX
             */
            loadTabContent: function(tabId, contentElement) {
                // Check if content is already loaded and cached
                if (this.settings.tabCache.has(tabId)) {
                    contentElement.innerHTML = this.settings.tabCache.get(tabId);
                    this.restoreTabFormData(tabId, contentElement);
                    this.initializeTabSpecificFunctionality(tabId);
                    return;
                }

                // Check if content is already loaded (not placeholder)
                const placeholder = contentElement.querySelector('.text-center.py-8');
                if (!placeholder) {
                    // Content is already loaded, cache it
                    this.settings.tabCache.set(tabId, contentElement.innerHTML);
                    this.restoreTabFormData(tabId, contentElement);
                    this.initializeTabSpecificFunctionality(tabId);
                    return;
                }

                // Show loading state
                contentElement.innerHTML = `
                    <div class="bg-white rounded shadow p-6">
                        <div class="text-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Зареждане...</h3>
                            <p class="text-gray-500">Моля, изчакайте</p>
                        </div>
                    </div>
                `;

                // Load content via AJAX
                const url = this.settings.config.baseUrl + '&tab=' + tabId;

                fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    // Extract the tab content from the response
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newContent = doc.getElementById('content-' + tabId);

                    if (newContent) {
                        contentElement.innerHTML = newContent.innerHTML;
                        // Cache the loaded content
                        this.settings.tabCache.set(tabId, newContent.innerHTML);
                        this.restoreTabFormData(tabId, contentElement);
                        this.initializeTabSpecificFunctionality(tabId);
                    } else {
                        // Fallback if content not found
                        this.showTabError(contentElement, 'Грешка при зареждане', 'Не можа да се зареди съдържанието');
                    }
                })
                .catch(error => {
                    console.error('Error loading tab content:', error);
                    this.showTabError(contentElement, 'Грешка в мрежата', 'Проверете интернет връзката');
                });
            },

            /**
             * Показване на грешка в таб
             */
            showTabError: function(contentElement, title, message) {
                contentElement.innerHTML = `
                    <div class="bg-white rounded shadow p-6">
                        <div class="text-center py-8">
                            <i class="ri-error-warning-line text-4xl text-red-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">${title}</h3>
                            <p class="text-gray-500">${message}</p>
                            <button onclick="location.reload()" class="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                Опитай отново
                            </button>
                        </div>
                    </div>
                `;
            },



            /**
             * Инициализиране на специфична функционалност за таб
             */
            initializeTabSpecificFunctionality: function(tabId) {
                const self = this;

                // Използваме setTimeout за да се уверим, че DOM е напълно зареден
                setTimeout(() => {
                    // Load module for current tab (now that content is loaded)
                    self.loadSettingsModule(tabId);

                    // Инициализиране на event delegation за динамично съдържание
                    self.initTabEventDelegation(tabId);
                }, 100);
            },

            /**
             * Инициализиране на event delegation за таб
             */
            initTabEventDelegation: function(tabId) {
                const self = this;
                const tabContent = document.getElementById('content-' + tabId);

                if (!tabContent) {
                    return;
                }

                // Премахваме стари event listeners за този таб ако има
                if (self.settings.tabEventListeners && self.settings.tabEventListeners[tabId]) {
                    self.settings.tabEventListeners[tabId].forEach(listener => {
                        tabContent.removeEventListener(listener.event, listener.handler);
                    });
                }

                // Инициализираме storage за event listeners
                if (!self.settings.tabEventListeners) {
                    self.settings.tabEventListeners = {};
                }
                self.settings.tabEventListeners[tabId] = [];

                // Event delegation за click събития в таба
                const clickHandler = function(e) {
                    self.handleTabClick(e, tabId);
                };

                tabContent.addEventListener('click', clickHandler);
                self.settings.tabEventListeners[tabId].push({
                    event: 'click',
                    handler: clickHandler
                });

                // Event delegation за form събития в таба
                const submitHandler = function(e) {
                    self.handleTabFormSubmit(e, tabId);
                };

                tabContent.addEventListener('submit', submitHandler);
                self.settings.tabEventListeners[tabId].push({
                    event: 'submit',
                    handler: submitHandler
                });

                // Event delegation за change събития в таба
                const changeHandler = function(e) {
                    self.handleTabChange(e, tabId);
                };

                tabContent.addEventListener('change', changeHandler);
                self.settings.tabEventListeners[tabId].push({
                    event: 'change',
                    handler: changeHandler
                });

                self.logDev && self.logDev('Event delegation initialized for tab: ' + tabId);
            },

            /**
             * Обработка на click събития в таб - делегира към специализираните модули
             */
            handleTabClick: function(e, tabId) {
                // Делегиране към специализираните модули
                const moduleMethodName = 'handle' + tabId.charAt(0).toUpperCase() + tabId.slice(1) + 'Click';
                if (typeof this[moduleMethodName] === 'function') {
                    this[moduleMethodName](e);
                }
            },

            /**
             * Обработка на form submit събития в таб - делегира към специализираните модули
             */
            handleTabFormSubmit: function(e, tabId) {
                // Делегиране към специализираните модули
                const moduleMethodName = 'handle' + tabId.charAt(0).toUpperCase() + tabId.slice(1) + 'FormSubmit';
                if (typeof this[moduleMethodName] === 'function') {
                    this[moduleMethodName](e);
                }
            },

            /**
             * Обработка на change събития в таб - делегира към специализираните модули
             */
            handleTabChange: function(e, tabId) {
                // Делегиране към специализираните модули
                const moduleMethodName = 'handle' + tabId.charAt(0).toUpperCase() + tabId.slice(1) + 'Change';
                if (typeof this[moduleMethodName] === 'function') {
                    this[moduleMethodName](e);
                }
            },

            /**
             * Актуализиране на URL за настройки
             */
            updateSettingsUrl: function(tabId) {
                const url = new URL(window.location);
                url.searchParams.set('tab', tabId);
                window.history.replaceState({}, '', url);
            },

            /**
             * Инициализиране на компоненти за настройки
             */
            initSettingsComponents: function() {
                // Инициализиране на системата за известия
                this.initSettingsNotifications();

                // Инициализиране на общи форми
                this.bindSettingsFormEvents();
            },

            /**
             * Инициализиране на глобални modal събития
             */
            initGlobalModalEvents: function() {
                const self = this;

                // Проверяваме дали вече сме добавили global listener
                if (self.settings.globalModalListenerAdded) {
                    return;
                }

                // Глобален event listener за модали
                document.addEventListener('click', function(e) {
                    const target = e.target;

                    // Затваряне на модал чрез close-modal класа
                    if (target.classList.contains('close-modal')) {
                        e.preventDefault();
                        const modal = target.closest('.fixed.inset-0');
                        if (modal) {
                            modal.remove();
                        }
                    }

                    // Затваряне на модал чрез клик извън съдържанието
                    if (target.classList.contains('fixed') && target.classList.contains('inset-0')) {
                        e.preventDefault();
                        target.remove();
                    }
                });

                // Escape key за затваряне на модали
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        const modal = document.querySelector('.fixed.inset-0');
                        if (modal) {
                            modal.remove();
                        }
                    }
                });

                self.settings.globalModalListenerAdded = true;
                self.logDev && self.logDev('Global modal events initialized');
            },

            /**
             * Инициализиране на известия за настройки
             */
            initSettingsNotifications: function() {
                // Използваме вградената система за известия от BackendModule
                // Проверяваме дали showAlert методът е наличен
                if (typeof this.showAlert !== 'function') {
                    this.logDev && this.logDev('showAlert method not found in BackendModule', 'warning');
                }
            },

            /**
             * Показване на нотификация за настройки
             * Използва BackendModule.showAlert метода
             */
            showSettingsNotification: function(message, type = 'info', duration = 3) {
                if (typeof this.showAlert === 'function') {
                    this.showAlert(type, message, duration);
                } else {
                    // Fallback към browser alert ако showAlert не е наличен
                    alert(message);
                    this.logDev && this.logDev('Fallback to browser alert: ' + message, 'warning');
                }
            },

            /**
             * Показване на потвърждение за настройки
             * Използва browser confirm за простота
             */
            showSettingsConfirm: function(message, callback) {
                if (confirm(message)) {
                    if (typeof callback === 'function') {
                        callback();
                    }
                }
            },

            /**
             * Инициализиране на форми за настройки
             */
            bindSettingsFormEvents: function() {
                // Общи event listeners за всички форми в настройките
                const forms = document.querySelectorAll('form[id*="settings"]');

                forms.forEach(form => {
                    // Предотвратяване на стандартното submit поведение
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        // Формите ще се обработват от специализираните модули
                    });
                });

                this.logDev && this.logDev('Settings forms initialized');
            },

            /**
             * Автоматично запазване на настройки
             */
            autoSaveSettings: function() {
                // Тази функция ще се използва от специализираните модули
                this.logDev && this.logDev('Auto-save triggered');
            }
        });
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initSettingsModule();
    });

})();