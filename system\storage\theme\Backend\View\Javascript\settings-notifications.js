/**
 * JavaScript модул за настройки на известия
 * Следва BackendModule pattern на темата Rakla.bg
 * Разширява основния settings модул
 */
(function() {
    'use strict';

    // Функция за инициализация на notifications модула
    function initNotificationsModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModuleWithNotifications();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул с функционалности за известия
    function extendBackendModuleWithNotifications() {
        Object.assign(BackendModule, {
            /**
             * Инициализиране на настройки за известия
             */
            initNotificationsSettings: function() {
                this.bindNotificationSettingsEvents();
                this.initEmailTemplateEditor();
                this.logDev && this.logDev('Notification settings module initialized');
            },

            /**
             * Свързване на събития за настройки за известия
             */
            bindNotificationSettingsEvents: function() {
                const self = this;

                // Форма за настройки за известия
                const notificationForm = document.getElementById('notification-settings-form');
                if (notificationForm) {
                    notificationForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveNotificationSettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-notification-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveNotificationSettings();
                    });
                }

                // Промяна на mail protocol
                const mailProtocolSelect = document.getElementById('config_mail_protocol');
                if (mailProtocolSelect) {
                    mailProtocolSelect.addEventListener('change', function() {
                        self.toggleSMTPFields(this.value === 'smtp');
                    });

                    // Инициализиране на състоянието
                    self.toggleSMTPFields(mailProtocolSelect.value === 'smtp');
                }

                // Тест на email настройки
                const testEmailButton = document.getElementById('test-email-settings');
                if (testEmailButton) {
                    testEmailButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testEmailSettings();
                    });
                }

                // Тест на SMS настройки
                const testSMSButton = document.getElementById('test-sms-settings');
                if (testSMSButton) {
                    testSMSButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testSMSSettings();
                    });
                }

                // Preview на email шаблон
                const previewButtons = document.querySelectorAll('.preview-email-template');
                previewButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.previewEmailTemplate(this.dataset.template);
                    });
                });

                // Reset на email шаблон
                const resetButtons = document.querySelectorAll('.reset-email-template');
                resetButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.resetEmailTemplate(this.dataset.template);
                    });
                });

                // Toggle за SMS настройки
                const smsEnabledToggle = document.getElementById('notification_sms_enabled');
                if (smsEnabledToggle) {
                    smsEnabledToggle.addEventListener('change', function() {
                        self.toggleSMSFields(this.checked);
                    });

                    // Инициализиране на състоянието
                    self.toggleSMSFields(smsEnabledToggle.checked);
                }

                // Изпращане на тестово известие
                const sendTestButton = document.getElementById('send-test-notification');
                if (sendTestButton) {
                    sendTestButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.sendTestNotification();
                    });
                }
            },

            /**
             * Обработка на click събития за notifications таб
             */
            handleNotificationsClick: function(e) {
                const target = e.target;
                const self = this;

                if (target.id === 'save-notification-settings') {
                    e.preventDefault();
                    self.saveNotificationSettings();
                } else if (target.id === 'test-email-settings') {
                    e.preventDefault();
                    self.testEmailSettings();
                } else if (target.id === 'test-sms-settings') {
                    e.preventDefault();
                    self.testSMSSettings();
                } else if (target.classList.contains('preview-email-template')) {
                    e.preventDefault();
                    self.previewEmailTemplate(target.dataset.template);
                } else if (target.classList.contains('reset-email-template')) {
                    e.preventDefault();
                    self.resetEmailTemplate(target.dataset.template);
                } else if (target.id === 'send-test-notification') {
                    e.preventDefault();
                    self.sendTestNotification();
                }
            },

            /**
             * Обработка на form submit събития за notifications таб
             */
            handleNotificationsFormSubmit: function(e) {
                e.preventDefault();
                const form = e.target;
                const self = this;

                if (form.id === 'notification-settings-form') {
                    self.saveNotificationSettings();
                }
            },

            /**
             * Обработка на change събития за notifications таб
             */
            handleNotificationsChange: function(e) {
                const target = e.target;
                const self = this;

                if (target.id === 'config_mail_protocol') {
                    self.toggleSMTPFields(target.value === 'smtp');
                } else if (target.id === 'notification_sms_enabled') {
                    self.toggleSMSFields(target.checked);
                }

                // Автоматично запазване при промяна на checkbox-и
                if (target.type === 'checkbox' && target.name && target.name.includes('setting')) {
                    self.autoSaveSettings();
                }
            },

            /**
             * Запазване на настройки за известия
             */
            saveNotificationSettings: function() {
                const self = this;
                const form = document.getElementById('notification-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateNotificationForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-notification-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.notifications_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Показване на предупреждения ако има
                        if (data.warnings && data.warnings.length > 0) {
                            data.warnings.forEach(warning => {
                                setTimeout(() => {
                                    self.showSettingsNotification(warning.message, warning.type);
                                }, 1000);
                            });
                        }

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateNotificationSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving notification settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за известия
             */
            validateNotificationForm: function(form) {
                const errors = [];

                // Валидация на SMTP настройки ако са активирани
                const mailProtocol = form.querySelector('[name="config_mail_protocol"]')?.value;
                if (mailProtocol === 'smtp') {
                    const smtpHost = form.querySelector('[name="config_mail_smtp_hostname"]')?.value;
                    const smtpPort = form.querySelector('[name="config_mail_smtp_port"]')?.value;

                    if (!smtpHost) {
                        errors.push('SMTP хост е задължителен');
                    }
                    if (!smtpPort || isNaN(smtpPort) || parseInt(smtpPort) <= 0) {
                        errors.push('SMTP порт трябва да бъде валидно число');
                    }
                }

                // Валидация на SMS настройки ако са активирани
                const smsEnabled = form.querySelector('[name="notification_sms_enabled"]')?.checked;
                if (smsEnabled) {
                    const smsProvider = form.querySelector('[name="sms_provider"]')?.value;
                    const smsApiKey = form.querySelector('[name="sms_api_key"]')?.value;

                    if (!smsProvider) {
                        errors.push('SMS доставчик е задължителен');
                    }
                    if (!smsApiKey) {
                        errors.push('SMS API ключ е задължителен');
                    }
                }

                // Валидация на email адрес
                const fromEmail = form.querySelector('[name="config_email"]')?.value;
                if (fromEmail && !this.isValidEmail(fromEmail)) {
                    errors.push('Невалиден email адрес');
                }

                return errors;
            },

            /**
             * Проверка за валиден email адрес
             */
            isValidEmail: function(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            },

            /**
             * Toggle на SMTP полета
             */
            toggleSMTPFields: function(enabled) {
                const smtpFields = document.querySelectorAll('.smtp-field');
                smtpFields.forEach(field => {
                    if (enabled) {
                        field.style.display = 'block';
                        field.classList.remove('hidden');
                    } else {
                        field.style.display = 'none';
                        field.classList.add('hidden');
                    }
                });
            },

            /**
             * Toggle на SMS полета
             */
            toggleSMSFields: function(enabled) {
                const smsFields = document.querySelectorAll('.sms-field');
                smsFields.forEach(field => {
                    if (enabled) {
                        field.style.display = 'block';
                        field.classList.remove('hidden');
                    } else {
                        field.style.display = 'none';
                        field.classList.add('hidden');
                    }
                });
            },

            /**
             * Тестване на email настройки
             */
            testEmailSettings: function() {
                const self = this;
                const testButton = document.getElementById('test-email-settings');

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                // Добавяне на email настройки за тестване
                const form = document.getElementById('notification-settings-form');
                if (form) {
                    const emailFields = ['config_mail_protocol', 'config_mail_smtp_hostname', 'config_mail_smtp_username', 'config_mail_smtp_password', 'config_mail_smtp_port', 'config_email'];
                    emailFields.forEach(fieldName => {
                        const field = form.querySelector(`[name="${fieldName}"]`);
                        if (field) {
                            formData.append(fieldName, field.value);
                        }
                    });
                }

                fetch(self.settings.config.ajaxUrls.test_email_settings || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    } else if (data.warning) {
                        self.showSettingsNotification(data.warning, 'warning');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing email settings:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-mail-line mr-2"></i>Тест Email';
                    }
                });
            },

            /**
             * Тестване на SMS настройки
             */
            testSMSSettings: function() {
                const self = this;
                const testButton = document.getElementById('test-sms-settings');

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                // Добавяне на SMS настройки за тестване
                const form = document.getElementById('notification-settings-form');
                if (form) {
                    const smsFields = ['sms_provider', 'sms_api_key', 'sms_api_secret', 'sms_sender_name'];
                    smsFields.forEach(fieldName => {
                        const field = form.querySelector(`[name="${fieldName}"]`);
                        if (field) {
                            formData.append(fieldName, field.value);
                        }
                    });
                }

                fetch(self.settings.config.ajaxUrls.test_sms_settings || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    } else if (data.warning) {
                        self.showSettingsNotification(data.warning, 'warning');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing SMS settings:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-message-2-line mr-2"></i>Тест SMS';
                    }
                });
            },

            /**
             * Preview на email шаблон
             */
            previewEmailTemplate: function(templateCode) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('template_code', templateCode);

                // Получаване на данните от шаблона
                const subjectField = document.querySelector(`[name="email_templates[${templateCode}][subject]"]`);
                const contentField = document.querySelector(`[name="email_templates[${templateCode}][content]"]`);

                if (subjectField && contentField) {
                    formData.append('subject', subjectField.value);
                    formData.append('content', contentField.value);
                }

                fetch(self.settings.config.ajaxUrls.preview_template || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.preview) {
                        self.showEmailPreviewModal(data.preview);
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error previewing email template:', error);
                    self.showSettingsNotification('Възникна грешка при preview', 'error');
                });
            },

            /**
             * Показване на preview modal за email
             */
            showEmailPreviewModal: function(preview) {
                // Създаване на modal за preview
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Preview на email шаблон</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <div class="p-4 overflow-y-auto max-h-[60vh]">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Тема:</label>
                                <div class="p-2 bg-gray-50 rounded border">${preview.subject || ''}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Съдържание:</label>
                                <div class="p-4 bg-gray-50 rounded border min-h-[200px]">
                                    ${preview.content || ''}
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end p-4 border-t">
                            <button class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Затвори
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
            },

            /**
             * Reset на email шаблон
             */
            resetEmailTemplate: function(templateCode) {
                const self = this;

                if (!self.showSettingsConfirm('Сигурни ли сте, че искате да възстановите шаблона до оригиналните настройки?')) {
                    return;
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('template_code', templateCode);

                fetch(self.settings.config.ajaxUrls.reset_template || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на полетата с новите стойности
                        if (data.template) {
                            const subjectField = document.querySelector(`[name="email_templates[${templateCode}][subject]"]`);
                            const contentField = document.querySelector(`[name="email_templates[${templateCode}][content]"]`);

                            if (subjectField) subjectField.value = data.template.subject || '';
                            if (contentField) contentField.value = data.template.content || '';
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error resetting email template:', error);
                    self.showSettingsNotification('Възникна грешка при възстановяването', 'error');
                });
            },

            /**
             * Изпращане на тестово известие
             */
            sendTestNotification: function() {
                const self = this;
                const sendButton = document.getElementById('send-test-notification');

                const testEmail = document.getElementById('test-notification-email')?.value || '';
                const testType = document.getElementById('test-notification-type')?.value || '';

                if (!testEmail) {
                    self.showSettingsNotification('Въведете email адрес за тестване', 'warning');
                    return;
                }

                if (!testType) {
                    self.showSettingsNotification('Изберете тип известие за тестване', 'warning');
                    return;
                }

                if (sendButton) {
                    sendButton.disabled = true;
                    sendButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Изпращане...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('test_email', testEmail);
                formData.append('test_type', testType);

                fetch(self.settings.config.ajaxUrls.send_test_notification || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error sending test notification:', error);
                    self.showSettingsNotification('Възникна грешка при изпращането', 'error');
                })
                .finally(() => {
                    if (sendButton) {
                        sendButton.disabled = false;
                        sendButton.innerHTML = '<i class="ri-send-plane-line mr-2"></i>Изпрати тест';
                    }
                });
            },

            /**
             * Инициализиране на email template editor
             */
            initEmailTemplateEditor: function() {
                // Добавяне на функционалност за автоматично resize на textarea
                const textareas = document.querySelectorAll('.email-template-content');
                textareas.forEach(textarea => {
                    textarea.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = this.scrollHeight + 'px';
                    });

                    // Инициализиране на размера
                    textarea.style.height = 'auto';
                    textarea.style.height = textarea.scrollHeight + 'px';
                });
            },

            /**
             * Актуализиране на показването на настройки за известия
             */
            updateNotificationSettingsDisplay: function(data) {
                // Актуализиране на статистики ако има
                if (data.statistics) {
                    const activeTemplatesElement = document.getElementById('active-email-templates-count');
                    if (activeTemplatesElement && data.statistics.active_email_templates !== undefined) {
                        activeTemplatesElement.textContent = data.statistics.active_email_templates;
                    }

                    const emailsSentElement = document.getElementById('emails-sent-today-count');
                    if (emailsSentElement && data.statistics.emails_sent_today !== undefined) {
                        emailsSentElement.textContent = data.statistics.emails_sent_today;
                    }
                }

                // Актуализиране на полетата с новите стойности
                Object.keys(data).forEach(key => {
                    if (key !== 'email_templates' && key !== 'statistics') {
                        const field = document.querySelector(`[name="${key}"]`);
                        if (field) {
                            if (field.type === 'checkbox') {
                                field.checked = !!data[key];
                            } else {
                                field.value = data[key] || '';
                            }
                        }
                    }
                });

                // Актуализиране на email шаблони
                if (data.email_templates) {
                    Object.keys(data.email_templates).forEach(templateCode => {
                        const templateData = data.email_templates[templateCode];

                        const subjectField = document.querySelector(`[name="email_templates[${templateCode}][subject]"]`);
                        const contentField = document.querySelector(`[name="email_templates[${templateCode}][content]"]`);
                        const statusField = document.querySelector(`[name="email_templates[${templateCode}][status]"]`);

                        if (subjectField) subjectField.value = templateData.subject || '';
                        if (contentField) contentField.value = templateData.content || '';
                        if (statusField) statusField.checked = !!templateData.status;
                    });
                }

                // Актуализиране на toggle състояния
                this.toggleSMTPFields(data.config_mail_protocol === 'smtp');
                this.toggleSMSFields(!!data.notification_sms_enabled);
            },

            /**
             * Показване на грешки при валидация
             */
            showValidationErrors: function(errors) {
                const self = this;

                if (Array.isArray(errors)) {
                    errors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                } else if (typeof errors === 'object') {
                    Object.keys(errors).forEach(field => {
                        const fieldElement = document.querySelector(`[name="${field}"]`);
                        if (fieldElement) {
                            fieldElement.classList.add('border-red-500');

                            // Премахване на грешката при промяна на полето
                            fieldElement.addEventListener('input', function() {
                                this.classList.remove('border-red-500');
                            }, { once: true });
                        }

                        self.showSettingsNotification(errors[field], 'error');
                    });
                }
            }
        });
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initNotificationsModule();
    });

})();
