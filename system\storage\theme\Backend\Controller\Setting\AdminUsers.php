<?php

namespace Theme25\Backend\Controller\Setting;

/**
 * Основен контролер за административни потребители
 *
 * @package Theme25\Backend\Controller\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class AdminUsers extends \Theme25\Controller {



    /**
     * Добавяне на нов административен потребител
     */
    public function add() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            // Валидация на входните данни
            $validationErrors = $this->validateUserData($this->requestPost());
            if (!empty($validationErrors)) {
                $this->sendJsonResponse([
                    'error' => 'Грешки при валидация',
                    'validation_errors' => $validationErrors
                ]);
                return;
            }

            // Създаване на потребителя
            $userData = $this->prepareUserDataForSave($this->requestPost());
            $userId = $this->adminUsers->addUser($userData);

            if ($userId) {
                $this->sendJsonResponse([
                    'success' => 'Потребителят е добавен успешно',
                    'user_id' => $userId
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при добавяне на потребителя'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error adding user: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при добавяне на потребителя'
            ]);
        }
    }

    /**
     * Редактиране на административен потребител
     */
    public function edit() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            $userId = $this->requestPost('user_id');
            if (!$userId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на потребителя']);
                return;
            }

            // Валидация на входните данни
            $validationErrors = $this->validateUserData($this->requestPost(), $userId);
            if (!empty($validationErrors)) {
                $this->sendJsonResponse([
                    'error' => 'Грешки при валидация',
                    'validation_errors' => $validationErrors
                ]);
                return;
            }

            // Актуализиране на потребителя
            $userData = $this->prepareUserDataForSave($this->requestPost());
            $success = $this->adminUsers->editUser($userId, $userData);

            if ($success) {
                $this->sendJsonResponse([
                    'success' => 'Потребителят е редактиран успешно'
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при редактиране на потребителя'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error editing user: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при редактиране на потребителя'
            ]);
        }
    }

    /**
     * Изтриване на административен потребител
     */
    public function delete() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            $userId = $this->requestPost('user_id');
            if (!$userId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на потребителя']);
                return;
            }

            // Проверка дали не се опитваме да изтрием главния администратор
            if ($userId == 1) {
                $this->sendJsonResponse(['error' => 'Не можете да изтриете главния администратор']);
                return;
            }

            // Проверка дали не се опитваме да изтрием текущия потребител
            if ($userId == $this->user->getId()) {
                $this->sendJsonResponse(['error' => 'Не можете да изтриете собствения си акаунт']);
                return;
            }

            $success = $this->adminUsers->deleteUser($userId);

            if ($success) {
                $this->sendJsonResponse([
                    'success' => 'Потребителят е изтрит успешно'
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при изтриване на потребителя'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error deleting user: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при изтриване на потребителя'
            ]);
        }
    }

    /**
     * Получаване на данни за потребител
     */
    public function getUser() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            $userId = $this->requestGet('user_id');
            if (!$userId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на потребителя']);
                return;
            }

            $user = $this->adminUsers->getUser($userId);

            if ($user) {
                $this->sendJsonResponse([
                    'user' => $user
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Потребителят не е намерен'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error getting user: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при зареждане на потребителя'
            ]);
        }
    }

    /**
     * Получаване на списък с всички потребители
     */
    public function getUsers() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            $users = $this->adminUsers->getAdminUsers();

            $this->sendJsonResponse([
                'users' => $users
            ]);

        } catch (\Exception $e) {
            error_log('Error getting users: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при зареждане на потребителите'
            ]);
        }
    }

    /**
     * Получаване на списък с потребителски групи
     */
    public function getGroups() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            $groups = $this->adminUsers->getUserGroups();

            $this->sendJsonResponse([
                'groups' => $groups
            ]);

        } catch (\Exception $e) {
            error_log('Error getting user groups: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при зареждане на групите'
            ]);
        }
    }

    /**
     * Добавяне на нова потребителска група
     */
    public function addGroup() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            // Валидация на входните данни
            $validationErrors = $this->validateGroupData($this->requestPost());
            if (!empty($validationErrors)) {
                $this->sendJsonResponse([
                    'error' => 'Грешки при валидация',
                    'validation_errors' => $validationErrors
                ]);
                return;
            }

            // Създаване на групата
            $groupData = $this->prepareGroupDataForSave($this->requestPost());
            $groupId = $this->adminUsers->addUserGroup($groupData);

            if ($groupId) {
                $this->sendJsonResponse([
                    'success' => 'Групата е добавена успешно',
                    'group_id' => $groupId
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при добавяне на групата'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error adding user group: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при добавяне на групата'
            ]);
        }
    }

    /**
     * Редактиране на потребителска група
     */
    public function editGroup() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            $groupId = $this->requestPost('user_group_id');
            if (!$groupId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на групата']);
                return;
            }

            // Валидация на входните данни
            $validationErrors = $this->validateGroupData($this->requestPost(), $groupId);
            if (!empty($validationErrors)) {
                $this->sendJsonResponse([
                    'error' => 'Грешки при валидация',
                    'validation_errors' => $validationErrors
                ]);
                return;
            }

            // Актуализиране на групата
            $groupData = $this->prepareGroupDataForSave($this->requestPost());
            $success = $this->adminUsers->editUserGroup($groupId, $groupData);

            if ($success) {
                $this->sendJsonResponse([
                    'success' => 'Групата е редактирана успешно'
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при редактиране на групата'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error editing user group: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при редактиране на групата'
            ]);
        }
    }

    /**
     * Изтриване на потребителска група
     */
    public function deleteGroup() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            $groupId = $this->requestPost('user_group_id');
            if (!$groupId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на групата']);
                return;
            }

            // Проверка дали не се опитваме да изтрием главната администраторска група
            if ($groupId == 1) {
                $this->sendJsonResponse(['error' => 'Не можете да изтриете главната администраторска група']);
                return;
            }

            // Проверка дали има потребители в групата
            $usersInGroup = $this->adminUsers->getUsersInGroup($groupId);
            if ($usersInGroup > 0) {
                $this->sendJsonResponse(['error' => 'Не можете да изтриете група, която съдържа потребители']);
                return;
            }

            $success = $this->adminUsers->deleteUserGroup($groupId);

            if ($success) {
                $this->sendJsonResponse([
                    'success' => 'Групата е изтрита успешно'
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при изтриване на групата'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error deleting user group: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при изтриване на групата'
            ]);
        }
    }

    /**
     * Промяна на статуса на потребител
     */
    public function toggleStatus() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            $userId = $this->requestPost('user_id');
            if (!$userId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на потребителя']);
                return;
            }

            // Проверка дали не се опитваме да деактивираме главния администратор
            if ($userId == 1) {
                $this->sendJsonResponse(['error' => 'Не можете да деактивирате главния администратор']);
                return;
            }

            $user = $this->adminUsers->getUser($userId);
            if (!$user) {
                $this->sendJsonResponse(['error' => 'Потребителят не е намерен']);
                return;
            }

            $newStatus = $user['status'] ? 0 : 1;
            $success = $this->adminUsers->editUser($userId, ['status' => $newStatus]);

            if ($success) {
                $this->sendJsonResponse([
                    'success' => $newStatus ? 'Потребителят е активиран' : 'Потребителят е деактивиран',
                    'new_status' => $newStatus
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при промяна на статуса'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error toggling user status: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при промяна на статуса'
            ]);
        }
    }

    /**
     * Възстановяване на парола
     */
    public function resetPassword() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            $userId = $this->requestPost('user_id');
            if (!$userId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на потребителя']);
                return;
            }

            $user = $this->adminUsers->getUser($userId);
            if (!$user) {
                $this->sendJsonResponse(['error' => 'Потребителят не е намерен']);
                return;
            }

            // Генериране на нова парола
            $newPassword = $this->generateRandomPassword();
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

            $success = $this->adminUsers->editUser($userId, ['password' => $hashedPassword]);

            if ($success) {
                $this->sendJsonResponse([
                    'success' => 'Паролата е възстановена успешно',
                    'new_password' => $newPassword
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при възстановяване на паролата'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error resetting password: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при възстановяване на паролата'
            ]);
        }
    }

    /**
     * Отключване на потребител
     */
    public function unlock() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsers');

            $userId = $this->requestPost('user_id');
            if (!$userId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на потребителя']);
                return;
            }

            $user = $this->adminUsers->getUser($userId);
            if (!$user) {
                $this->sendJsonResponse(['error' => 'Потребителят не е намерен']);
                return;
            }

            $success = $this->adminUsers->editUser($userId, [
                'locked_until' => null,
                'failed_login_attempts' => 0
            ]);

            if ($success) {
                $this->sendJsonResponse([
                    'success' => 'Потребителят е отключен успешно'
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при отключване на потребителя'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error unlocking user: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при отключване на потребителя'
            ]);
        }
    }

    /**
     * Валидация на данните за потребител
     */
    private function validateUserData($data, $userId = null) {
        $errors = [];

        // Зареждане на модела ако не е зареден
        if (!isset($this->adminUsers)) {
            $this->loadModelAs('setting/admin_users', 'adminUsers');
        }

        // Валидация на потребителско име
        if (empty($data['username'])) {
            $errors['username'] = 'Потребителското име е задължително';
        } elseif (strlen($data['username']) < 3) {
            $errors['username'] = 'Потребителското име трябва да бъде поне 3 символа';
        } else {
            // Проверка за уникалност
            $existingUser = $this->adminUsers->getUserByUsername($data['username']);
            if ($existingUser && (!$userId || $existingUser['user_id'] != $userId)) {
                $errors['username'] = 'Това потребителско име вече се използва';
            }
        }

        // Валидация на email
        if (empty($data['email'])) {
            $errors['email'] = 'Email адресът е задължителен';
        } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Невалиден email адрес';
        } else {
            // Проверка за уникалност
            $existingUser = $this->adminUsers->getUserByEmail($data['email']);
            if ($existingUser && (!$userId || $existingUser['user_id'] != $userId)) {
                $errors['email'] = 'Този email адрес вече се използва';
            }
        }

        // Валидация на име и фамилия
        if (empty($data['firstname'])) {
            $errors['firstname'] = 'Името е задължително';
        }

        if (empty($data['lastname'])) {
            $errors['lastname'] = 'Фамилията е задължителна';
        }

        // Валидация на парола (само при добавяне или ако е въведена)
        if (!$userId || !empty($data['password'])) {
            if (empty($data['password'])) {
                $errors['password'] = 'Паролата е задължителна';
            } elseif (strlen($data['password']) < 6) {
                $errors['password'] = 'Паролата трябва да бъде поне 6 символа';
            } elseif ($data['password'] !== $data['confirm_password']) {
                $errors['confirm_password'] = 'Паролите не съвпадат';
            }
        }

        // Валидация на потребителска група
        if (empty($data['user_group_id'])) {
            $errors['user_group_id'] = 'Потребителската група е задължителна';
        }

        return $errors;
    }

    /**
     * Валидация на данните за група
     */
    private function validateGroupData($data, $groupId = null) {
        $errors = [];

        // Зареждане на модела ако не е зареден
        if (!isset($this->adminUsers)) {
            $this->loadModelAs('setting/admin_users', 'adminUsers');
        }

        // Валидация на име на група
        if (empty($data['name'])) {
            $errors['name'] = 'Името на групата е задължително';
        } elseif (strlen($data['name']) < 3) {
            $errors['name'] = 'Името на групата трябва да бъде поне 3 символа';
        } else {
            // Проверка за уникалност
            $existingGroup = $this->adminUsers->getUserGroupByName($data['name']);
            if ($existingGroup && (!$groupId || $existingGroup['user_group_id'] != $groupId)) {
                $errors['name'] = 'Това име на група вече се използва';
            }
        }

        return $errors;
    }

    /**
     * Подготовка на данните за запазване на потребител
     */
    private function prepareUserDataForSave($data) {
        $userData = [
            'username' => trim($data['username']),
            'email' => trim($data['email']),
            'firstname' => trim($data['firstname']),
            'lastname' => trim($data['lastname']),
            'user_group_id' => (int)$data['user_group_id'],
            'status' => isset($data['status']) ? (int)$data['status'] : 1
        ];

        // Добавяне на парола само ако е въведена
        if (!empty($data['password'])) {
            $userData['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        return $userData;
    }

    /**
     * Подготовка на данните за запазване на група
     */
    private function prepareGroupDataForSave($data) {
        return [
            'name' => trim($data['name']),
            'description' => trim($data['description'] ?? ''),
            'permission' => $data['permission'] ?? []
        ];
    }

    /**
     * Проверка за AJAX заявка
     */
    private function checkAjaxRequest() {
        if (!$this->isAjax()) {
            $this->sendJsonResponse(['error' => 'Невалидна заявка']);
            exit;
        }

        if (!$this->hasPermission('modify', 'setting/admin_users')) {
            $this->sendJsonResponse(['error' => 'Нямате права за тази операция']);
            exit;
        }
    }

    /**
     * Изпращане на JSON отговор
     */
    private function sendJsonResponse($data) {
        $this->setJsonResponse($data);
    }

    /**
     * Генериране на случайна парола
     */
    private function generateRandomPassword($length = 12) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        $charactersLength = strlen($characters);

        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, $charactersLength - 1)];
        }

        return $password;
    }
}
