<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Административни потребители</h1>
    <p class="text-gray-600 mt-1">Управлявайте административни потребители, групи и права на достъп</p>
</div>

<form id="admin-users-settings-form" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Admin Users Table -->
            <div class="bg-white rounded shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">
                        <i class="ri-user-settings-line mr-2"></i>
                        Административни потребители
                    </h2>
                    <button type="button" 
                            id="add-admin-user"
                            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        <i class="ri-user-add-line mr-2"></i>
                        Добави потребител
                    </button>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Потребител
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Email
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Група
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Статус
                                </th>
                                {# <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Последен вход
                                </th> #}
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Действия
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for user in admin_users %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            {% if user.image %}
                                                <img class="h-10 w-10 rounded-full" src="{{ user.image }}" alt="{{ user.firstname }} {{ user.lastname }}">
                                            {% else %}
                                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <i class="ri-user-line text-gray-600"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ user.firstname }}{{' '}}{{ user.lastname }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                @{{ user.username }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ user.email }}
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ user.user_group }}
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    {% if user.status %}
                                        {% if user.locked_until %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <i class="ri-lock-line mr-1"></i>
                                                Заключен
                                            </span>
                                        {% else %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i class="ri-check-line mr-1"></i>
                                                Активен
                                            </span>
                                        {% endif %}
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <i class="ri-close-line mr-1"></i>
                                            Неактивен
                                        </span>
                                    {% endif %}
                                </td>
                                {# <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% if user.last_login %}
                                        {{ user.last_login|date('d.m.Y H:i') }}
                                    {% else %}
                                        Никога
                                    {% endif %}
                                </td> #}
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button type="button" 
                                                data-user-id="{{ user.user_id }}"
                                                class="edit-user text-blue-600 hover:text-blue-900"
                                                title="Редактирай">
                                            <i class="ri-edit-line"></i>
                                        </button>
                                        
                                        {% if user.locked_until %}
                                            <button type="button" 
                                                    data-user-id="{{ user.user_id }}"
                                                    class="unlock-user text-green-600 hover:text-green-900"
                                                    title="Отключи">
                                                <i class="ri-lock-unlock-line"></i>
                                            </button>
                                        {% else %}
                                            <button type="button" 
                                                    data-user-id="{{ user.user_id }}"
                                                    class="toggle-user-status text-orange-600 hover:text-orange-900"
                                                    title="{{ user.status ? 'Деактивирай' : 'Активирай' }}">
                                                <i class="ri-{{ user.status ? 'pause' : 'play' }}-line"></i>
                                            </button>
                                        {% endif %}
                                        
                                        <button type="button" 
                                                data-user-id="{{ user.user_id }}"
                                                class="reset-user-password text-purple-600 hover:text-purple-900"
                                                title="Възстанови парола">
                                            <i class="ri-key-line"></i>
                                        </button>
                                        
                                        {% if user.user_id != 1 %}
                                            <button type="button" 
                                                    data-user-id="{{ user.user_id }}"
                                                    class="delete-user text-red-600 hover:text-red-900"
                                                    title="Изтрий">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- User Groups -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">
                        <i class="ri-group-line mr-2"></i>
                        Групи потребители
                    </h2>
                    <button type="button" 
                            id="add-user-group"
                            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        <i class="ri-add-line mr-2"></i>
                        Добави група
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for group in user_groups %}
                    <div class="border border-gray-200 rounded p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-medium text-gray-900">{{ group.name }}</h3>
                            <div class="flex space-x-1">
                                <button type="button" 
                                        data-group-id="{{ group.user_group_id }}"
                                        class="edit-user-group text-blue-600 hover:text-blue-900"
                                        title="Редактирай">
                                    <i class="ri-edit-line"></i>
                                </button>
                                <button type="button" 
                                        data-group-id="{{ group.user_group_id }}"
                                        class="manage-permissions text-green-600 hover:text-green-900"
                                        title="Управление на права">
                                    <i class="ri-shield-user-line"></i>
                                </button>
                                {% if group.user_group_id != 1 %}
                                    <button type="button" 
                                            data-group-id="{{ group.user_group_id }}"
                                            class="delete-user-group text-red-600 hover:text-red-900"
                                            title="Изтрий">
                                        <i class="ri-delete-bin-line"></i>
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                        
                        <p class="text-sm text-gray-600 mb-3">{{ group.description }}</p>
                        
                        <div class="flex justify-between text-xs text-gray-500">
                            <span>{{ group.users_count }} потребители</span>
                            <span>{{ group.permissions_count }} права</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Security Settings -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-shield-check-line mr-2"></i>
                    Настройки за сигурност
                </h2>
                
                <div class="space-y-6">
                    <!-- Password Policy -->
                    <div>
                        <h3 class="text-md font-medium text-gray-900 mb-3">Политика за пароли</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Минимална дължина
                                </label>
                                <input type="number" 
                                       name="security_settings[password_min_length]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       value="{{ security_settings.password_min_length ?? 8 }}"
                                       min="6"
                                       max="32">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Изтичане (дни)
                                </label>
                                <input type="number" 
                                       name="security_settings[password_expiry_days]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       value="{{ security_settings.password_expiry_days ?? 90 }}"
                                       min="30"
                                       max="365">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mt-4">
                            <div class="flex items-center justify-start space-x-2">
                                <input type="checkbox" 
                                       name="security_settings[password_require_uppercase]"
                                       class="toggle-switch"
                                       {{ security_settings.password_require_uppercase ? 'checked' : '' }}>
                                <span class="text-sm text-gray-700">Изисква главни букви</span>
                            </div>
                            
                            <div class="flex items-center justify-start space-x-2">
                                <input type="checkbox" 
                                       name="security_settings[password_require_lowercase]"
                                       class="toggle-switch"
                                       {{ security_settings.password_require_lowercase ? 'checked' : '' }}>
                                <span class="text-sm text-gray-700">Изисква малки букви</span>
                            </div>
                            
                            <div class="flex items-center justify-start space-x-2">
                                <input type="checkbox" 
                                       name="security_settings[password_require_numbers]"
                                       class="toggle-switch"
                                       {{ security_settings.password_require_numbers ? 'checked' : '' }}>
                                <span class="text-sm text-gray-700">Изисква цифри</span>
                            </div>
                            
                            <div class="flex items-center justify-start space-x-2">
                                <input type="checkbox" 
                                       name="security_settings[password_require_symbols]"
                                       class="toggle-switch"
                                       {{ security_settings.password_require_symbols ? 'checked' : '' }}>
                                <span class="text-sm text-gray-700">Изисква символи</span>
                            </div>
                        </div>
                    </div>

                    <!-- Login Security -->
                    <div>
                        <h3 class="text-md font-medium text-gray-900 mb-3">Сигурност при вход</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Максимални опити за вход
                                </label>
                                <input type="number" 
                                       name="security_settings[max_login_attempts]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       value="{{ security_settings.max_login_attempts ?? 5 }}"
                                       min="3"
                                       max="20">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Време за заключване (минути)
                                </label>
                                <input type="number" 
                                       name="security_settings[lockout_duration]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       value="{{ security_settings.lockout_duration ?? 30 }}"
                                       min="5"
                                       max="1440">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mt-4">
                            <div class="flex items-center justify-start space-x-2">
                                <input type="checkbox" 
                                       name="security_settings[two_factor_auth]"
                                       class="toggle-switch"
                                       {{ security_settings.two_factor_auth ? 'checked' : '' }}>
                                <span class="text-sm text-gray-700">Двуфакторна автентикация</span>
                            </div>
                            
                            <div class="flex items-center justify-start space-x-2">
                                <input type="checkbox" 
                                       name="security_settings[login_notifications]"
                                       class="toggle-switch"
                                       {{ security_settings.login_notifications ? 'checked' : '' }}>
                                <span class="text-sm text-gray-700">Известия за вход</span>
                            </div>
                        </div>
                    </div>



                    <!-- IP Whitelist е преместен в Security таба -->


                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">


            <!-- Quick Actions -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-tools-line mr-2"></i>
                    Бързи действия
                </h2>
                
                <div class="space-y-3">
                    <button type="button" 
                            id="save-admin-users-settings"
                            class="w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors">
                        <i class="ri-save-line mr-2"></i>
                        Запази настройки
                    </button>



                    <button type="button" 
                            onclick="location.reload()"
                            class="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        <i class="ri-refresh-line mr-2"></i>
                        Презареди
                    </button>
                </div>
            </div>



            <!-- Security Tips -->
            <div class="bg-yellow-50 border border-yellow-200 rounded p-6 mt-6">
                <h2 class="text-lg font-semibold text-yellow-800 mb-4">
                    <i class="ri-lightbulb-line mr-2"></i>
                    Съвети за сигурност
                </h2>
                
                <div class="space-y-3 text-sm text-yellow-700">
                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Използвайте силни пароли с главни букви, цифри и символи</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Активирайте двуфакторна автентикация за по-висока сигурност</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Редовно проверявайте логовете за подозрителна активност</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Ограничете IP адресите за критични административни акаунти</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
