

<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Основни настройки</h1>
    <p class="text-gray-600 mt-1">Управлявайте основната информация за вашия магазин</p>
</div>

<form id="basic-settings-form" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Store Information -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded shadow p-6 settings-card">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Информация за магазина</h2>
                <div class="space-y-4">
                    <div>
                        <label for="store_name" class="block text-sm font-medium text-gray-700 mb-1">
                            Име на магазина <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="store_name" name="store_name"
                               class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                               value="{{ store_name|default('Rakla') }}" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Лого на магазина</label>
                        <div class="mt-4">
                            <div id="store-logo-container" class="max-w-xs" style="max-width: max-content;">
                                {% if store_logo %}
                                <!-- Показване на съществуващо лого -->
                                <div class="relative group">
                                    <div class="aspect-auto rounded-lg overflow-hidden border border-gray-200" style="width: 192px; height: auto; min-height: 96px;">
                                        <img src="{{ store_logo_url }}" alt="Лого на магазина" id="store-logo-preview" class="w-full h-full object-contain">
                                    </div>
                                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Промени лого" data-action="select-store-logo">
                                            <div class="w-5 h-5 flex items-center justify-center">
                                                <i class="ri-folder-image-line"></i>
                                            </div>
                                        </button>
                                        <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахни лого" data-action="remove-store-logo">
                                            <div class="w-5 h-5 flex items-center justify-center">
                                                <i class="ri-delete-bin-line"></i>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                                {% else %}
                                <!-- Placeholder за добавяне на лого -->
                                <div class="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" style="width: 192px; height: 96px;">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <button type="button" data-action="select-store-logo" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                                            <i class="ri-folder-image-line ri-lg"></i>
                                        </button>
                                    </div>
                                    <p class="text-xs text-gray-400 text-center">Няма лого</p>
                                </div>
                                {% endif %}
                            </div>
                            <input type="hidden" name="store_logo" value="{{ store_logo }}" id="input-store-logo">
                            <p class="text-xs text-gray-500 mt-2">Препоръчителен размер: 200x60px. Поддържани формати: JPG, PNG, GIF, SVG</p>
                        </div>
                    </div>
                    
                    <div>
                        <label for="store_email" class="block text-sm font-medium text-gray-700 mb-1">
                            Имейл адрес <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="email" id="store_email" name="store_email"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                   value="{{ store_email|default('<EMAIL>') }}" required>
                            <button type="button" id="test-email" class="test-email-button absolute right-2 top-2 text-gray-400 hover:text-primary">
                                <i class="ri-mail-check-line"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <label for="store-phone" class="block text-sm font-medium text-gray-700 mb-1">Телефон *</label>
                        <input type="tel" id="store-phone" name="store_phone" 
                               class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                               value="{{ store_phone|default('') }}" required>
                    </div>
                    
                    <div>
                        <label for="store-address" class="block text-sm font-medium text-gray-700 mb-1">Адрес *</label>
                        <textarea id="store-address" name="store_address" rows="3" 
                                  class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                  required>{{ store_address|default('') }}</textarea>
                    </div>
                </div>
            </div>
            
            <!-- Social Media -->
            <div class="bg-white rounded shadow p-6 mt-6 settings-card">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Социални мрежи</h2>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 flex items-center justify-center text-blue-400 mr-2">
                            <i class="ri-facebook-fill ri-lg"></i>
                        </div>
                        <div class="flex-1 relative">
                            <input type="url" id="facebook-url" name="facebook_url" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   placeholder="Facebook URL" value="{{ facebook_url|default('') }}">
                            <button type="button" class="test-url-button absolute right-2 top-2 text-gray-400 hover:text-primary">
                                <i class="ri-external-link-line"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-10 h-10 flex items-center justify-center text-pink-600 mr-2">
                            <i class="ri-instagram-fill ri-lg"></i>
                        </div>
                        <div class="flex-1 relative">
                            <input type="url" id="instagram-url" name="instagram_url" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   placeholder="Instagram URL" value="{{ instagram_url|default('') }}">
                            <button type="button" class="test-url-button absolute right-2 top-2 text-gray-400 hover:text-primary">
                                <i class="ri-external-link-line"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-10 h-10 flex items-center justify-center text-red-600 mr-2">
                            <i class="ri-youtube-fill ri-lg"></i>
                        </div>
                        <div class="flex-1 relative">
                            <input type="url" id="youtube-url" name="youtube_url" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   placeholder="YouTube URL" value="{{ youtube_url|default('') }}">
                            <button type="button" class="test-url-button absolute right-2 top-2 text-gray-400 hover:text-primary">
                                <i class="ri-external-link-line"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-10 h-10 flex items-center justify-center text-red-600 mr-2">
                            <i class="ri-tiktok-fill ri-lg"></i>
                        </div>
                        <div class="flex-1 relative">
                            <input type="url" id="tiktok-url" name="tiktok_url" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   placeholder="TikTok URL" value="{{ tiktok_url|default('') }}">
                            <button type="button" class="test-url-button absolute right-2 top-2 text-gray-400 hover:text-primary">
                                <i class="ri-external-link-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Settings Sidebar -->
        <div>
            <!-- General Settings -->
            <div class="bg-white rounded shadow p-6 settings-card">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Общи настройки</h2>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-800">Поддръжка</h3>
                            <p class="text-xs text-gray-500">Активиране на чат за поддръжка</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" name="support_chat" value="1" {{ support_chat ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-800">Отзиви</h3>
                            <p class="text-xs text-gray-500">Разрешаване на отзиви за продукти</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" name="reviews_enabled" value="1" {{ reviews_enabled ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-800">Наличност</h3>
                            <p class="text-xs text-gray-500">Показване на наличност в магазина</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" name="stock_display" value="1" {{ stock_display ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-800">Режим за поддръжка</h3>
                            <p class="text-xs text-gray-500">Временно затваряне на магазина</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" name="maintenance_mode" value="1" {{ maintenance_mode ? 'checked' : '' }}>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Currency and Language -->
            <div class="bg-white rounded shadow p-6 mt-6 settings-card">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Валута и език</h2>
                <div class="space-y-4">
                    <div>
                        <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">Основна валута</label>
                        <select id="currency" name="currency" 
                                class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                            {% for curr in currencies %}
                            <option value="{{ curr.code }}" {{ curr.code == currency ? 'selected' : '' }}>
                                {{ curr.title }} ({{ curr.code }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label for="language" class="block text-sm font-medium text-gray-700 mb-1">Основен език</label>
                        <select id="language" name="language" 
                                class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                            {% for lang in languages %}
                            <option value="{{ lang.code }}" {{ lang.code == language ? 'selected' : '' }}>
                                {{ lang.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- SEO Settings -->
            <div class="bg-white rounded shadow p-6 mt-6 settings-card">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">SEO настройки</h2>
                <div class="space-y-4">
                    <div>
                        <label for="meta-title" class="block text-sm font-medium text-gray-700 mb-1">Мета заглавие *</label>
                        <input type="text" id="meta-title" name="meta_title" 
                               class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                               value="{{ meta_title|default('Rakla - Онлайн магазин') }}" required>
                    </div>
                    
                    <div>
                        <label for="meta-description" class="block text-sm font-medium text-gray-700 mb-1">Мета описание</label>
                        <textarea id="meta-description" name="meta_description" rows="3" 
                                  class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                  placeholder="Кратко описание на магазина за търсачките">{{ meta_description|default('') }}</textarea>
                    </div>
                    
                    <div>
                        <label for="meta-keywords" class="block text-sm font-medium text-gray-700 mb-1">Ключови думи</label>
                        <input type="text" id="meta-keywords" name="meta_keywords" 
                               class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                               value="{{ meta_keywords|default('') }}" 
                               placeholder="ключова дума 1, ключова дума 2">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Save Button -->
    <div class="flex justify-end">
        <button type="button" id="save-basic-settings"
                class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">
            <i class="ri-save-line mr-2"></i>
            Запази настройките
        </button>
    </div>
</form>

<script>
// Конфигурация за JavaScript модула
window.settingsConfig = {
    userToken: '{{ user_token }}',
    saveUrl: '{{ save_url }}',
    uploadLogoUrl: '{{ upload_logo_url }}',
    removeLogoUrl: '{{ save_url }}', // Ще се използва същия URL с различен параметър
    getLogoInfoUrl: '{{ save_url }}', // Ще се използва същия URL с различен параметър
    testEmailUrl: '{{ test_email_url }}',
    testSocialUrl: '{{ test_social_url }}',
    validateContactUrl: '{{ save_url }}' // Ще се използва същия URL с различен параметър
};
</script>

{% if tab_script_url %}
<script type="text/javascript" src="{{ tab_script_url }}"></script>
{% endif %}
