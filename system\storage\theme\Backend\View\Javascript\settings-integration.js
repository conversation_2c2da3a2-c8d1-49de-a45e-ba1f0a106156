/**
 * JavaScript модул за настройки на интеграции
 * Следва BackendModule pattern на темата Rakla.bg
 * Разширява основния settings модул
 */
(function() {
    'use strict';

    // Функция за инициализация на integration модула
    function initIntegrationModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModuleWithIntegration();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул с функционалности за интеграции
    function extendBackendModuleWithIntegration() {
        Object.assign(BackendModule, {
            /**
             * Инициализиране на настройки за интеграции
             */
            initIntegrationSettings: function() {
                this.bindIntegrationSettingsEvents();
                this.initWebhookManager();
                this.logDev && this.logDev('Integration settings module initialized');
            },

            /**
             * Свързване на събития за настройки за интеграции
             */
            bindIntegrationSettingsEvents: function() {
                const self = this;

                // Форма за настройки за интеграции
                const integrationForm = document.getElementById('integration-settings-form');
                if (integrationForm) {
                    integrationForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveIntegrationSettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-integration-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveIntegrationSettings();
                    });
                }

                // Toggle за интеграции
                const integrationToggles = document.querySelectorAll('.integration-toggle');
                integrationToggles.forEach(toggle => {
                    toggle.addEventListener('change', function() {
                        self.toggleIntegration(this.dataset.integration, this.dataset.type, this.checked);
                    });
                });

                // Тест на интеграции
                const testButtons = document.querySelectorAll('.test-integration');
                testButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testIntegration(this.dataset.integration, this.dataset.type);
                    });
                });

                // Генериране на API ключове
                const generateApiButtons = document.querySelectorAll('.generate-api-key');
                generateApiButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.generateApiKey(this.dataset.api);
                    });
                });
            },

            /**
             * Обработка на click събития за integration таб
             */
            handleIntegrationClick: function(e) {
                const target = e.target;
                const self = this;

                if (target.id === 'save-integration-settings') {
                    e.preventDefault();
                    self.saveIntegrationSettings();
                } else if (target.classList.contains('test-integration')) {
                    e.preventDefault();
                    self.testIntegration(target.dataset.integration, target.dataset.type);
                } else if (target.classList.contains('generate-api-key')) {
                    e.preventDefault();
                    self.generateApiKey(target.dataset.api);
                }
            },

            /**
             * Обработка на form submit събития за integration таб
             */
            handleIntegrationFormSubmit: function(e) {
                e.preventDefault();
                const form = e.target;
                const self = this;

                if (form.id === 'integration-settings-form') {
                    self.saveIntegrationSettings();
                }
            },

            /**
             * Обработка на change събития за integration таб
             */
            handleIntegrationChange: function(e) {
                const target = e.target;
                const self = this;

                if (target.classList.contains('integration-toggle')) {
                    self.toggleIntegration(target.dataset.integration, target.dataset.type, target.checked);
                }

                // Автоматично запазване при промяна на checkbox-и
                if (target.type === 'checkbox' && target.name && target.name.includes('setting')) {
                    self.autoSaveSettings();
                }
            },

            /**
             * Запазване на настройки за интеграции
             */
            saveIntegrationSettings: function() {
                const self = this;
                const form = document.getElementById('integration-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateIntegrationForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-integration-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.integrations_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateIntegrationSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving integration settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за интеграции
             */
            validateIntegrationForm: function(form) {
                const errors = [];

                // Валидация на Google Analytics
                const gaEnabled = form.querySelector('[name="analytics_integrations[google_analytics][status]"]')?.checked;
                if (gaEnabled) {
                    const trackingId = form.querySelector('[name="analytics_integrations[google_analytics][tracking_id]"]')?.value || '';
                    if (!trackingId) {
                        errors.push('Google Analytics Tracking ID е задължителен');
                    } else if (!/^(UA|G)-[0-9A-Z-]+$/.test(trackingId)) {
                        errors.push('Невалиден формат на Google Analytics Tracking ID');
                    }
                }

                // Валидация на Facebook Pixel
                const fbEnabled = form.querySelector('[name="analytics_integrations[facebook_pixel][status]"]')?.checked;
                if (fbEnabled) {
                    const pixelId = form.querySelector('[name="analytics_integrations[facebook_pixel][pixel_id]"]')?.value || '';
                    if (!pixelId) {
                        errors.push('Facebook Pixel ID е задължителен');
                    } else if (!/^\d+$/.test(pixelId)) {
                        errors.push('Facebook Pixel ID трябва да бъде число');
                    }
                }

                // Валидация на API интеграции
                const apiIntegrations = form.querySelectorAll('[name*="api_integrations"]');
                apiIntegrations.forEach(field => {
                    if (field.type === 'checkbox' && field.checked) {
                        const apiName = field.name.match(/api_integrations\[([^\]]+)\]/)?.[1];
                        if (apiName) {
                            const apiKeyField = form.querySelector(`[name="api_integrations[${apiName}][api_key]"]`);
                            if (apiKeyField && !apiKeyField.value.trim()) {
                                errors.push(`API ключ за ${apiName} е задължителен`);
                            }
                        }
                    }
                });

                return errors;
            },

            /**
             * Toggle на интеграция
             */
            toggleIntegration: function(integrationCode, integrationType, enabled) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('integration_code', integrationCode);
                formData.append('integration_type', integrationType);
                formData.append('enabled', enabled ? '1' : '0');

                fetch(self.settings.config.ajaxUrls.toggle_integration || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'Интеграцията е актуализирана', 'success');

                        // Актуализиране на визуалното състояние
                        const integrationCard = document.querySelector(`[data-integration="${integrationCode}"][data-type="${integrationType}"]`);
                        if (integrationCard) {
                            const card = integrationCard.closest('.integration-card');
                            if (enabled) {
                                card.classList.remove('opacity-50');
                                card.classList.add('border-green-200', 'bg-green-50');
                            } else {
                                card.classList.add('opacity-50');
                                card.classList.remove('border-green-200', 'bg-green-50');
                            }
                        }
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при актуализирането', 'error');

                        // Връщане на toggle-а в предишното състояние
                        const toggle = document.querySelector(`[data-integration="${integrationCode}"][data-type="${integrationType}"].integration-toggle`);
                        if (toggle) {
                            toggle.checked = !enabled;
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error toggling integration:', error);
                    self.showSettingsNotification('Възникна грешка при промяната', 'error');
                });
            },

            /**
             * Тестване на интеграция
             */
            testIntegration: function(integrationCode, integrationType) {
                const self = this;
                const testButton = document.querySelector(`[data-integration="${integrationCode}"][data-type="${integrationType}"].test-integration`);

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('integration_code', integrationCode);
                formData.append('integration_type', integrationType);

                fetch(self.settings.config.ajaxUrls.test_integration || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'Интеграцията работи правилно', 'success');
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при тестването', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing integration:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-play-line mr-2"></i>Тест';
                    }
                });
            }
        });
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initIntegrationModule();
    });

})();
