

<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Настройки за сигурност</h1>
    <p class="text-gray-600 mt-1">Управлявайте сигурността на вашия административен панел</p>
</div>

<form id="security-settings-form" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Security Settings -->
        <div class="lg:col-span-1">
            <!-- IP Restrictions -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-shield-line mr-2"></i>
                    IP ограничения
                </h2>
                
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               id="ip_restriction_enabled"
                               name="ip_restriction_enabled"
                               class="toggle-switch"
                               {{ ip_restriction_enabled ? 'checked' : '' }}>
                        <div>
                            <label for="ip_restriction_enabled" class="text-sm font-medium text-gray-700">
                                Активиране на IP ограничения
                            </label>
                            <p class="text-xs text-gray-500">Разрешава достъп само от определени IP адреси</p>
                        </div>
                    </div>

                    <div id="ip-restriction-fields" class="{{ ip_restriction_enabled ? '' : 'hidden' }}">
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <label class="block text-sm font-medium text-gray-700">
                                    Разрешени IP адреси
                                </label>
                                <button type="button" id="add-ip-address" class="px-3 py-1 bg-primary text-white text-sm rounded hover:bg-primary/90">
                                    <i class="ri-add-line mr-1"></i>Добави IP
                                </button>
                            </div>

                            <div class="border border-gray-300 rounded">
                                <table class="w-full">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Адрес</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Описание</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
                                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                                        </tr>
                                    </thead>
                                    <tbody id="ip-addresses-table" class="bg-white divide-y divide-gray-200">
                                        <!-- IP адресите ще се зареждат динамично -->
                                    </tbody>
                                </table>

                                <div id="no-ip-addresses" class="text-center py-8 text-gray-500 hidden">
                                    <i class="ri-information-line text-2xl mb-2"></i>
                                    <p>Няма добавени IP адреси</p>
                                    <p class="text-xs">Кликнете "Добави IP" за да добавите нов адрес</p>
                                </div>
                            </div>

                            <p class="text-xs text-gray-500 mt-2">
                                Вашият текущ IP: <strong>{{ current_ip }}</strong>
                                <span id="current-ip-status" class="ml-2"></span>
                            </p>

                            <!-- Скрито поле за съхранение на IP адресите -->
                            <input type="hidden" id="allowed_ips_text" name="allowed_ips_text" value="{{ allowed_ips_text ?? '' }}">
                        </div>

                        <div class="flex space-x-2">
                            <button type="button" 
                                    id="test-ip-restriction"
                                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                                <i class="ri-shield-check-line mr-2"></i>
                                Тест IP
                            </button>
                            <button type="button" 
                                    onclick="document.getElementById('allowed_ips_text').value += '{{ current_ip }}\n'"
                                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                                <i class="ri-add-line mr-2"></i>
                                Добави текущия IP
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Session Security -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-time-line mr-2"></i>
                    Сигурност на сесиите
                </h2>
                
                <div class="space-y-4">
                    <div>
                        <label for="session_timeout" class="block text-sm font-medium text-gray-700 mb-1">
                            Timeout на сесия
                        </label>
                        <select id="session_timeout" 
                                name="session_timeout"
                                class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                            {% for value, label in session_timeout_options %}
                            <option value="{{ value }}" {{ session_timeout == value ? 'selected' : '' }}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                        <p class="text-xs text-gray-500 mt-1">Време след което сесията изтича при неактивност</p>
                    </div>

                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               id="force_ssl"
                               name="force_ssl"
                               class="toggle-switch"
                               {{ force_ssl ? 'checked' : '' }}>
                        <div>
                            <label for="force_ssl" class="text-sm font-medium text-gray-700">
                                Принудително SSL (HTTPS)
                            </label>
                            <p class="text-xs text-gray-500">Пренасочва всички HTTP заявки към HTTPS</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Two-Factor Authentication -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-smartphone-line mr-2"></i>
                    Двуфакторна автентикация
                </h2>
                
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               id="two_factor_enabled"
                               name="two_factor_enabled"
                               class="toggle-switch"
                               {{ two_factor_enabled ? 'checked' : '' }}>
                        <div>
                            <label for="two_factor_enabled" class="text-sm font-medium text-gray-700">
                                Активиране на 2FA
                            </label>
                            <p class="text-xs text-gray-500">Изисква допълнителен код от мобилно приложение</p>
                        </div>
                    </div>

                    {% if two_factor_enabled %}
                    <div class="bg-blue-50 border border-blue-200 rounded p-4">
                        <div class="flex items-center">
                            <i class="ri-information-line text-blue-500 mr-2"></i>
                            <div>
                                <p class="text-sm text-blue-800 font-medium">Двуфакторната автентикация е активна</p>
                                <p class="text-xs text-blue-600">Използвайте приложение като Google Authenticator или Authy</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>



            <!-- Logging -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-file-list-line mr-2"></i>
                    Логиране
                </h2>
                
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               id="admin_activity_log"
                               name="admin_activity_log"
                               class="toggle-switch"
                               {{ admin_activity_log ? 'checked' : '' }}>
                        <div>
                            <label for="admin_activity_log" class="text-sm font-medium text-gray-700">
                                Лог на активността на администраторите
                            </label>
                            <p class="text-xs text-gray-500">Записва всички действия на администраторите</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               id="failed_login_log"
                               name="failed_login_log"
                               class="toggle-switch"
                               {{ failed_login_log ? 'checked' : '' }}>
                        <div>
                            <label for="failed_login_log" class="text-sm font-medium text-gray-700">
                                Лог на неуспешни опити за вход
                            </label>
                            <p class="text-xs text-gray-500">Записва всички неуспешни опити за вход</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Panel -->
        <div class="lg:col-span-1">
            <!-- Password Policy Settings -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-shield-check-line mr-2"></i>
                    Политика за пароли
                </h2>

                <div class="space-y-6">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Минимална дължина
                            </label>
                            <input type="number"
                                   name="security_settings[password_min_length]"
                                   class="w-full px-3 py-2 border border-gray-300 rounded"
                                   value="{{ security_settings.password_min_length ?? 8 }}"
                                   min="6"
                                   max="32">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Изтичане (дни)
                            </label>
                            <input type="number"
                                   name="security_settings[password_expiry_days]"
                                   class="w-full px-3 py-2 border border-gray-300 rounded"
                                   value="{{ security_settings.password_expiry_days ?? 90 }}"
                                   min="30"
                                   max="365">
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="security_settings[password_require_uppercase]"
                                   class="toggle-switch"
                                   {{ security_settings.password_require_uppercase ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Изисква главни букви</span>
                        </div>

                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="security_settings[password_require_lowercase]"
                                   class="toggle-switch"
                                   {{ security_settings.password_require_lowercase ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Изисква малки букви</span>
                        </div>

                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="security_settings[password_require_numbers]"
                                   class="toggle-switch"
                                   {{ security_settings.password_require_numbers ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Изисква цифри</span>
                        </div>

                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="security_settings[password_require_symbols]"
                                   class="toggle-switch"
                                   {{ security_settings.password_require_symbols ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Изисква символи</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Security Settings -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-lock-line mr-2"></i>
                    Сигурност при вход
                </h2>

                <div class="space-y-6">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Максимални опити за вход
                            </label>
                            <input type="number"
                                   name="security_settings[max_login_attempts]"
                                   class="w-full px-3 py-2 border border-gray-300 rounded"
                                   value="{{ security_settings.max_login_attempts ?? 5 }}"
                                   min="3"
                                   max="20">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Време за заключване (минути)
                            </label>
                            <input type="number"
                                   name="security_settings[lockout_duration]"
                                   class="w-full px-3 py-2 border border-gray-300 rounded"
                                   value="{{ security_settings.lockout_duration ?? 30 }}"
                                   min="5"
                                   max="1440">
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="security_settings[two_factor_auth]"
                                   class="toggle-switch"
                                   {{ security_settings.two_factor_auth ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Двуфакторна автентикация</span>
                        </div>

                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="security_settings[login_notifications]"
                                   class="toggle-switch"
                                   {{ security_settings.login_notifications ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Известия за вход</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Tips -->
            <div class="bg-yellow-50 border border-yellow-200 rounded p-6 mt-6">
                <h2 class="text-lg font-semibold text-yellow-800 mb-4">
                    <i class="ri-lightbulb-line mr-2"></i>
                    Съвети за сигурност
                </h2>

                <div class="space-y-3 text-sm text-yellow-700">
                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Използвайте силни пароли с главни букви, цифри и символи</span>
                    </div>

                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Активирайте двуфакторна автентикация за по-висока сигурност</span>
                    </div>

                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Редовно проверявайте логовете за подозрителна активност</span>
                    </div>

                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Ограничете IP адресите за критични административни акаунти</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Save Button -->
    <div class="flex justify-end">
        <button type="button" id="save-security-settings"
                class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">
            <i class="ri-save-line mr-2"></i>
            Запази настройки
        </button>
    </div>
</form>

{% if tab_script_url %}
<script type="text/javascript" src="{{ tab_script_url }}"></script>
{% endif %}
