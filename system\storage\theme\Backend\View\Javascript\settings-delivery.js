/**
 * JavaScript модул за настройки на доставка
 * Следва BackendModule pattern на темата Rakla.bg
 * Разширява основния settings модул
 */
(function() {
    'use strict';

    // Функция за инициализация на delivery модула
    function initDeliveryModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModuleWithDelivery();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул с функционалности за доставка
    function extendBackendModuleWithDelivery() {
        Object.assign(BackendModule, {
            /**
             * Инициализиране на настройки за доставка
             */
            initDeliverySettings: function() {
                this.bindDeliverySettingsEvents();
                this.initShippingMethodsSortable();
                this.initZonesTable();
                this.logDev && this.logDev('Delivery settings module initialized');
            },

            /**
             * Свързване на събития за настройки за доставка
             */
            bindDeliverySettingsEvents: function() {
                const self = this;

                // Форма за настройки за доставка
                const deliveryForm = document.getElementById('delivery-settings-form');
                if (deliveryForm) {
                    deliveryForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveDeliverySettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-delivery-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveDeliverySettings();
                    });
                }

                // Toggle за методи на доставка
                const deliveryToggles = document.querySelectorAll('.delivery-method-toggle');
                deliveryToggles.forEach(toggle => {
                    toggle.addEventListener('change', function() {
                        self.toggleDeliveryMethod(this.dataset.method, this.checked);
                    });
                });

                // Тест на методи на доставка
                const testButtons = document.querySelectorAll('.test-delivery-method');
                testButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testDeliveryMethod(this.dataset.method);
                    });
                });

                // Конфигуриране на методи на доставка
                const configButtons = document.querySelectorAll('.config-delivery-method');
                configButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.configureDeliveryMethod(this.dataset.method);
                    });
                });

                // Управление на зони
                const addZoneButton = document.getElementById('add-delivery-zone');
                if (addZoneButton) {
                    addZoneButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.showAddZoneModal();
                    });
                }
            },

            /**
             * Обработка на click събития за delivery таб
             */
            handleDeliveryClick: function(e) {
                const target = e.target;
                const self = this;

                if (target.id === 'save-delivery-settings') {
                    e.preventDefault();
                    self.saveDeliverySettings();
                } else if (target.classList.contains('test-delivery-method')) {
                    e.preventDefault();
                    self.testDeliveryMethod(target.dataset.method);
                } else if (target.classList.contains('config-delivery-method')) {
                    e.preventDefault();
                    self.configureDeliveryMethod(target.dataset.method);
                } else if (target.id === 'add-delivery-zone') {
                    e.preventDefault();
                    self.showAddZoneModal();
                } else if (target.classList.contains('edit-zone')) {
                    e.preventDefault();
                    self.editZone(target.dataset.zoneId);
                } else if (target.classList.contains('delete-zone')) {
                    e.preventDefault();
                    self.deleteZone(target.dataset.zoneId);
                }
            },

            /**
             * Обработка на form submit събития за delivery таб
             */
            handleDeliveryFormSubmit: function(e) {
                e.preventDefault();
                const form = e.target;
                const self = this;

                if (form.id === 'delivery-settings-form') {
                    self.saveDeliverySettings();
                } else if (form.id === 'delivery-method-config-form') {
                    self.saveDeliveryMethodConfig(form);
                } else if (form.id === 'add-zone-form') {
                    self.submitAddZoneForm(form);
                }
            },

            /**
             * Обработка на change събития за delivery таб
             */
            handleDeliveryChange: function(e) {
                const target = e.target;
                const self = this;

                if (target.classList.contains('delivery-method-toggle')) {
                    self.toggleDeliveryMethod(target.dataset.method, target.checked);
                }

                // Автоматично запазване при промяна на checkbox-и
                if (target.type === 'checkbox' && target.name && target.name.includes('setting')) {
                    self.autoSaveSettings();
                }
            },

            /**
             * Запазване на настройки за доставка
             */
            saveDeliverySettings: function() {
                const self = this;
                const form = document.getElementById('delivery-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateDeliveryForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-delivery-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.delivery_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateDeliverySettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving delivery settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за доставка
             */
            validateDeliveryForm: function(form) {
                const errors = [];

                // Валидация на безплатна доставка
                const freeDeliveryEnabled = form.querySelector('[name="delivery_free_delivery_enabled"]')?.checked;
                if (freeDeliveryEnabled) {
                    const freeDeliveryAmount = form.querySelector('[name="delivery_free_delivery_amount"]')?.value;
                    if (!freeDeliveryAmount || parseFloat(freeDeliveryAmount) <= 0) {
                        errors.push('Сумата за безплатна доставка трябва да бъде положително число');
                    }
                }

                // Валидация на тегло за доставка
                const maxWeight = form.querySelector('[name="delivery_max_weight"]')?.value;
                if (maxWeight && parseFloat(maxWeight) <= 0) {
                    errors.push('Максималното тегло за доставка трябва да бъде положително число');
                }

                // Валидация на размери за доставка
                const maxLength = form.querySelector('[name="delivery_max_length"]')?.value;
                const maxWidth = form.querySelector('[name="delivery_max_width"]')?.value;
                const maxHeight = form.querySelector('[name="delivery_max_height"]')?.value;

                if (maxLength && parseFloat(maxLength) <= 0) {
                    errors.push('Максималната дължина трябва да бъде положително число');
                }
                if (maxWidth && parseFloat(maxWidth) <= 0) {
                    errors.push('Максималната ширина трябва да бъде положително число');
                }
                if (maxHeight && parseFloat(maxHeight) <= 0) {
                    errors.push('Максималната височина трябва да бъде положително число');
                }

                // Валидация на активни методи на доставка
                const activeDeliveryMethods = form.querySelectorAll('.delivery-method-toggle:checked');
                if (activeDeliveryMethods.length === 0) {
                    errors.push('Трябва да има поне един активен метод на доставка');
                }

                return errors;
            },

            /**
             * Toggle на метод на доставка
             */
            toggleDeliveryMethod: function(methodCode, enabled) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_code', methodCode);
                formData.append('enabled', enabled ? '1' : '0');

                fetch(self.settings.config.ajaxUrls.toggle_delivery_method || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'Методът на доставка е актуализиран', 'success');

                        // Актуализиране на визуалното състояние
                        const methodCard = document.querySelector(`[data-method="${methodCode}"]`);
                        if (methodCard) {
                            const card = methodCard.closest('.delivery-method-card');
                            if (enabled) {
                                card.classList.remove('opacity-50');
                                card.classList.add('border-green-200', 'bg-green-50');
                            } else {
                                card.classList.add('opacity-50');
                                card.classList.remove('border-green-200', 'bg-green-50');
                            }
                        }
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при актуализирането', 'error');

                        // Връщане на toggle-а в предишното състояние
                        const toggle = document.querySelector(`[data-method="${methodCode}"].delivery-method-toggle`);
                        if (toggle) {
                            toggle.checked = !enabled;
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error toggling delivery method:', error);
                    self.showSettingsNotification('Възникна грешка при промяната', 'error');
                });
            },

            /**
             * Тестване на метод на доставка
             */
            testDeliveryMethod: function(methodCode) {
                const self = this;
                const testButton = document.querySelector(`[data-method="${methodCode}"].test-delivery-method`);

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_code', methodCode);

                fetch(self.settings.config.ajaxUrls.test_delivery_method || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'Методът на доставка работи правилно', 'success');
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при тестването', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing delivery method:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-play-line mr-2"></i>Тест';
                    }
                });
            },

            /**
             * Показване на modal за добавяне на зона
             */
            showAddZoneModal: function() {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на зона за доставка</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-zone-form" class="p-4">
                            <div class="space-y-4">
                                <div>
                                    <label for="zone_name" class="block text-sm font-medium text-gray-700 mb-1">Име на зоната</label>
                                    <input type="text" id="zone_name" name="name" required 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="zone_description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
                                    <textarea id="zone_description" name="description" rows="3" 
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"></textarea>
                                </div>
                                <div>
                                    <label for="zone_price" class="block text-sm font-medium text-gray-700 mb-1">Цена за доставка</label>
                                    <input type="number" id="zone_price" name="price" step="0.01" min="0" required 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="zone_status" name="status" value="1" checked 
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                    <label for="zone_status" class="ml-2 block text-sm text-gray-900">Активна</label>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                    Отказ
                                </button>
                                <button type="submit" id="save-new-zone" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                    <i class="ri-save-line mr-2"></i>Запази
                                </button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(modal);
            },

            /**
             * Инициализиране на sortable за shipping методи
             */
            initShippingMethodsSortable: function() {
                const shippingMethodsList = document.getElementById('shipping-methods-list');
                if (shippingMethodsList && typeof Sortable !== 'undefined') {
                    new Sortable(shippingMethodsList, {
                        animation: 150,
                        ghostClass: 'sortable-ghost',
                        onEnd: () => {
                            this.reorderShippingMethods();
                        }
                    });
                }
            },

            /**
             * Преподреждане на shipping методи
             */
            reorderShippingMethods: function() {
                const self = this;
                const shippingMethodsList = document.getElementById('shipping-methods-list');

                if (!shippingMethodsList) return;

                const methodCodes = [];
                const methodRows = shippingMethodsList.querySelectorAll('[data-method]');

                methodRows.forEach((row, index) => {
                    const methodCode = row.dataset.method;
                    if (methodCode) {
                        methodCodes.push({
                            code: methodCode,
                            sort_order: index + 1
                        });
                    }
                });

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_order', JSON.stringify(methodCodes));

                fetch(self.settings.config.ajaxUrls.reorder_shipping_methods || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification('Подредбата е запазена', 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error reordering shipping methods:', error);
                    self.showSettingsNotification('Възникна грешка при преподреждането', 'error');
                });
            },

            /**
             * Инициализиране на таблицата с зони
             */
            initZonesTable: function() {
                // Инициализиране на функционалност за таблицата с зони
                this.logDev && this.logDev('Zones table initialized');
            }
        });
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initDeliveryModule();
    });

})();
