<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за настройки за административни потребители
 *
 * Този контролер управлява логиката за показване и обработка на настройките за
 * административни потребители, включително създаване, редактиране, права на достъп,
 * групи потребители, сигурност и активност.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class AdminUsers extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за настройките за административни потребители
     */
    public function prepareData() {
        $this->prepareAdminUsersData()
             ->prepareUserGroupsData()
             ->preparePermissionsData()
             ->prepareSecuritySettingsData()
             ->prepareActivityLogsData()
             ->prepareUrlsAndActions()
             ->prepareValidationRules();
    }

    /**
     * Подготвя данните за административните потребители
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareAdminUsersData() {
        try {
            // Debug информация
            error_log('Опит за зареждане на модел setting/admin_users');
            
            $this->loadModelAs('setting/admin_users', 'adminUsersSettings');
            
            // Проверка дали моделът е зареден правилно
            if (!$this->adminUsersSettings) {
                error_log('Моделът adminUsersSettings е null');
                throw new \Exception('Моделът setting/admin_users не може да бъде зареден');
            }
            
            error_log('Моделът е зареден успешно: ' . get_class($this->adminUsersSettings));
            
            $admin_users = $this->adminUsersSettings->getAdminUsers();
            
            $this->setData('admin_users', $admin_users);
            
        } catch (\Exception $e) {
            $this->setError('Грешка при зареждане на административни потребители: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData('admin_users', [
                [
                    'user_id' => 1,
                    'username' => 'admin',
                    'firstname' => 'Администратор',
                    'lastname' => 'Системен',
                    'email' => '<EMAIL>',
                    'user_group' => 'Администратори',
                    'user_group_id' => 1,
                    'status' => 1,
                    'last_login' => date('Y-m-d H:i:s'),
                    'date_added' => date('Y-m-d H:i:s'),
                    'image' => '',
                    'failed_attempts' => 0,
                    'locked_until' => null
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за групите потребители
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUserGroupsData() {
        try {
            $this->loadModelAs('setting/admin_users', 'adminUsersSettings');
            $user_groups = $this->adminUsersSettings->getAvailableRoles();
            
            $this->setData('user_groups', $user_groups);
            
        } catch (\Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('user_groups', [
                [
                    'user_group_id' => 1,
                    'name' => 'Администратори',
                    'description' => 'Пълен достъп до всички функции',
                    'users_count' => 1,
                    'permissions_count' => 0,
                    'created_date' => date('Y-m-d H:i:s')
                ],
                [
                    'user_group_id' => 2,
                    'name' => 'Мениджъри',
                    'description' => 'Достъп до продукти, поръчки и клиенти',
                    'users_count' => 0,
                    'permissions_count' => 15,
                    'created_date' => date('Y-m-d H:i:s')
                ],
                [
                    'user_group_id' => 3,
                    'name' => 'Оператори',
                    'description' => 'Ограничен достъп до поръчки',
                    'users_count' => 0,
                    'permissions_count' => 8,
                    'created_date' => date('Y-m-d H:i:s')
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за правата на достъп
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePermissionsData() {
        try {
            $this->loadModelAs('setting/admin_users', 'adminUsersSettings');
            $permissions = $this->adminUsersSettings->getAvailableRoles();
            
            $this->setData('permissions', $permissions);
            
        } catch (\Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('permissions', [
                'catalog' => [
                    'name' => 'Каталог',
                    'description' => 'Управление на продукти, категории, производители',
                    'routes' => [
                        'catalog/product' => 'Продукти',
                        'catalog/category' => 'Категории',
                        'catalog/manufacturer' => 'Производители',
                        'catalog/option' => 'Опции',
                        'catalog/attribute' => 'Атрибути'
                    ]
                ],
                'sale' => [
                    'name' => 'Продажби',
                    'description' => 'Управление на поръчки, клиенти, ваучери',
                    'routes' => [
                        'sale/order' => 'Поръчки',
                        'sale/customer' => 'Клиенти',
                        'sale/voucher' => 'Ваучери',
                        'sale/return' => 'Връщания'
                    ]
                ],
                'marketing' => [
                    'name' => 'Маркетинг',
                    'description' => 'Маркетингови кампании и промоции',
                    'routes' => [
                        'marketing/marketing' => 'Маркетинг',
                        'marketing/coupon' => 'Купони',
                        'marketing/affiliate' => 'Партньори'
                    ]
                ],
                'system' => [
                    'name' => 'Система',
                    'description' => 'Системни настройки и конфигурация',
                    'routes' => [
                        'setting/setting' => 'Настройки',
                        'user/user' => 'Потребители',
                        'user/user_group' => 'Групи потребители',
                        'tool/backup' => 'Backup'
                    ]
                ],
                'report' => [
                    'name' => 'Отчети',
                    'description' => 'Статистики и отчети',
                    'routes' => [
                        'report/sale_order' => 'Отчет продажби',
                        'report/product_viewed' => 'Най-гледани продукти',
                        'report/customer_online' => 'Онлайн клиенти'
                    ]
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за настройки за сигурност
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareSecuritySettingsData() {
        try {
            $this->loadModelAs('setting/admin_users', 'adminUsersSettings');
            $security_settings = $this->adminUsersSettings->getSettings();
            
            $this->setData('security_settings', $security_settings);
            
        } catch (\Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('security_settings', [
                'password_min_length' => 8,
                'password_require_uppercase' => 1,
                'password_require_lowercase' => 1,
                'password_require_numbers' => 1,
                'password_require_symbols' => 0,
                'password_expiry_days' => 90,
                'max_login_attempts' => 5,
                'lockout_duration' => 30,
                'session_timeout' => 3600,
                'force_password_change' => 0,
                'two_factor_auth' => 0,
                'ip_whitelist_enabled' => 0,
                'ip_whitelist' => '',
                'login_notifications' => 1,
                'activity_logging' => 1,
                'auto_logout_idle' => 1
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за логове на активност
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareActivityLogsData() {
        try {
            $this->loadModelAs('setting/admin_users', 'adminUsersSettings');
            $activity_logs = [];
            
            $this->setData('activity_logs', $activity_logs);
            
        } catch (\Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('activity_logs', [
                [
                    'log_id' => 1,
                    'user_id' => 1,
                    'username' => 'admin',
                    'action' => 'login',
                    'description' => 'Успешен вход в системата',
                    'ip_address' => '*************',
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'date_added' => date('Y-m-d H:i:s', strtotime('-5 minutes'))
                ],
                [
                    'log_id' => 2,
                    'user_id' => 1,
                    'username' => 'admin',
                    'action' => 'setting_update',
                    'description' => 'Актуализирани настройки на магазина',
                    'ip_address' => '*************',
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'date_added' => date('Y-m-d H:i:s', strtotime('-15 minutes'))
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя URL-и за AJAX заявки и действия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUrlsAndActions() {
        $urls = [
            'save_url' => $this->getAdminLink('setting/setting/admin_users_save'),
            'add_user_url' => $this->getAdminLink('setting/admin_users/add'),
            'edit_user_url' => $this->getAdminLink('setting/admin_users/edit'),
            'delete_user_url' => $this->getAdminLink('setting/admin_users/delete'),
            'get_user_url' => $this->getAdminLink('setting/admin_users/getUser'),
            'get_users_url' => $this->getAdminLink('setting/admin_users/getUsers'),
            'get_user_groups_url' => $this->getAdminLink('setting/admin_users/getGroups'),
            'toggle_user_status_url' => $this->getAdminLink('setting/admin_users/toggleStatus'),
            'reset_password_url' => $this->getAdminLink('setting/admin_users/resetPassword'),
            'unlock_user_url' => $this->getAdminLink('setting/admin_users/unlock'),
            'add_user_group_url' => $this->getAdminLink('setting/admin_users/addGroup'),
            'edit_user_group_url' => $this->getAdminLink('setting/admin_users/editGroup'),
            'delete_user_group_url' => $this->getAdminLink('setting/admin_users/deleteGroup'),
            'update_permissions_url' => $this->getAdminLink('setting/setting/update_permissions'),
            'test_security_url' => $this->getAdminLink('setting/setting/test_security'),
            'export_activity_logs_url' => $this->getAdminLink('setting/setting/export_activity_logs'),
            'clear_activity_logs_url' => $this->getAdminLink('setting/setting/clear_activity_logs')
        ];

        foreach ($urls as $key => $url) {
            $this->setData($key, $url);
        }

        return $this;
    }

    /**
     * Подготвя правила за валидация
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareValidationRules() {
        $validation_rules = [
            'username' => [
                'required' => true,
                'min_length' => 3,
                'max_length' => 20,
                'pattern' => '^[a-zA-Z0-9_]+$'
            ],
            'password' => [
                'required' => true,
                'min_length' => 8,
                'require_uppercase' => true,
                'require_lowercase' => true,
                'require_numbers' => true,
                'require_symbols' => false
            ],
            'email' => [
                'required' => true,
                'type' => 'email'
            ],
            'firstname' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 32
            ],
            'lastname' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 32
            ],
            'user_group_id' => [
                'required' => true,
                'type' => 'number',
                'min' => 1
            ]
        ];

        $this->setData('validation_rules', $validation_rules);

        return $this;
    }

    /**
     * Получава статистики за административните потребители
     *
     * @return array
     */
    private function getAdminUsersStatistics() {
        try {
            $this->loadModelAs('setting/admin_users', 'adminUsersModel');

            return $this->adminUsersModel->getAdminUsersStatistics();
            
        } catch (\Exception $e) {
            return [
                'total_users' => 1,
                'active_users' => 1,
                'locked_users' => 0,
                'total_groups' => 3,
                'online_users' => 1,
                'failed_logins_today' => 0,
                'successful_logins_today' => 5
            ];
        }
    }

    /**
     * Проверява дали има предупреждения за потребителите
     *
     * @return array
     */
    private function getAdminUsersWarnings() {
        $warnings = [];
        
        // Проверка за слаби пароли
        $security_settings = $this->getData('security_settings') ?: [];
        if (empty($security_settings['password_require_uppercase']) || 
            empty($security_settings['password_require_numbers'])) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Политиката за пароли не е достатъчно строга. Препоръчваме да изисквате главни букви и цифри.'
            ];
        }
        
        // Проверка за двуфакторна автентикация
        if (empty($security_settings['two_factor_auth'])) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Двуфакторната автентикация не е активирана. Препоръчваме да я включите за по-висока сигурност.'
            ];
        }
        
        // Проверка за заключени потребители
        $statistics = $this->getAdminUsersStatistics();
        if ($statistics['locked_users'] > 0) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Има ' . $statistics['locked_users'] . ' заключени потребители. Проверете дали не са необходими действия.'
            ];
        }
        
        // Проверка за неуспешни опити за вход
        if ($statistics['failed_logins_today'] > 10) {
            $warnings[] = [
                'type' => 'error',
                'message' => 'Много неуспешни опити за вход днес (' . $statistics['failed_logins_today'] . '). Възможна атака!'
            ];
        }
        
        return $warnings;
    }

    /**
     * Получава конфигурацията за JavaScript модула
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        return [
            'userToken' => $this->getUserToken(),
            'ajaxUrls' => [
                'save' => $this->getData('save_url'),
                'admin_users_save' => $this->getData('save_url'),
                'add_user' => $this->getData('add_user_url'),
                'edit_user' => $this->getData('edit_user_url'),
                'delete_user' => $this->getData('delete_user_url'),
                'get_user' => $this->getData('get_user_url'),
                'get_users' => $this->getData('get_users_url'),
                'get_user_groups' => $this->getData('get_user_groups_url'),
                'get_groups' => $this->getData('get_user_groups_url'), // Alias
                'add_group' => $this->getData('add_user_group_url'),
                'add_user_group' => $this->getData('add_user_group_url'), // Alias
                'edit_group' => $this->getData('edit_user_group_url'),
                'edit_user_group' => $this->getData('edit_user_group_url'), // Alias
                'delete_group' => $this->getData('delete_user_group_url'),
                'delete_user_group' => $this->getData('delete_user_group_url'), // Alias
                'toggle_user_status' => $this->getData('toggle_user_status_url'),
                'reset_password' => $this->getData('reset_password_url'),
                'unlock_user' => $this->getData('unlock_user_url')
            ],
            'validationRules' => $this->getData('validation_rules'),
            'statistics' => $this->getAdminUsersStatistics(),
            'warnings' => $this->getAdminUsersWarnings()
        ];
    }

    /**
     * Добавяне на нов административен потребител
     */
    public function add() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsersModel');

            // Валидация на входните данни
            $validationErrors = $this->validateUserData($this->request->post);
            if (!empty($validationErrors)) {
                $this->sendJsonResponse([
                    'error' => 'Грешки при валидация',
                    'validation_errors' => $validationErrors
                ]);
                return;
            }

            // Създаване на потребителя
            $userData = $this->prepareUserDataForSave($this->request->post);
            $userId = $this->adminUsersModel->addUser($userData);

            if ($userId) {
                $this->sendJsonResponse([
                    'success' => 'Потребителят е добавен успешно',
                    'user_id' => $userId
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при добавяне на потребителя'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error adding user: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при добавяне на потребителя'
            ]);
        }
    }

    /**
     * Редактиране на административен потребител
     */
    public function edit() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsersModel');

            $userId = $this->request->post['user_id'] ?? null;
            if (!$userId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на потребителя']);
                return;
            }

            // Валидация на входните данни
            $validationErrors = $this->validateUserData($this->request->post, $userId);
            if (!empty($validationErrors)) {
                $this->sendJsonResponse([
                    'error' => 'Грешки при валидация',
                    'validation_errors' => $validationErrors
                ]);
                return;
            }

            // Актуализиране на потребителя
            $userData = $this->prepareUserDataForSave($this->request->post);
            $success = $this->adminUsersModel->editUser($userId, $userData);

            if ($success) {
                $this->sendJsonResponse([
                    'success' => 'Потребителят е редактиран успешно'
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при редактиране на потребителя'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error editing user: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при редактиране на потребителя'
            ]);
        }
    }

    /**
     * Изтриване на административен потребител
     */
    public function delete() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsersModel');

            $userId = $this->request->post['user_id'] ?? null;
            if (!$userId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на потребителя']);
                return;
            }

            // Проверка дали не се опитваме да изтрием главния администратор
            if ($userId == 1) {
                $this->sendJsonResponse(['error' => 'Не можете да изтриете главния администратор']);
                return;
            }

            // Проверка дали не се опитваме да изтрием текущия потребител
            if ($userId == $this->user->getId()) {
                $this->sendJsonResponse(['error' => 'Не можете да изтриете собствения си акаунт']);
                return;
            }

            $success = $this->adminUsersModel->deleteUser($userId);

            if ($success) {
                $this->sendJsonResponse([
                    'success' => 'Потребителят е изтрит успешно'
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Грешка при изтриване на потребителя'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error deleting user: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при изтриване на потребителя'
            ]);
        }
    }

    /**
     * Получаване на данни за потребител
     */
    public function getUser() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsersModel');

            $userId = $this->request->get['user_id'] ?? null;
            if (!$userId) {
                $this->sendJsonResponse(['error' => 'Липсва ID на потребителя']);
                return;
            }

            $user = $this->adminUsersModel->getUser($userId);

            if ($user) {
                $this->sendJsonResponse([
                    'user' => $user
                ]);
            } else {
                $this->sendJsonResponse([
                    'error' => 'Потребителят не е намерен'
                ]);
            }

        } catch (\Exception $e) {
            error_log('Error getting user: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при зареждане на потребителя'
            ]);
        }
    }

    /**
     * Получаване на списък с потребителски групи
     */
    public function getGroups() {
        $this->checkAjaxRequest();

        try {
            $this->loadModelAs('setting/admin_users', 'adminUsersModel');

            $groups = $this->adminUsersModel->getUserGroups();

            $this->sendJsonResponse([
                'groups' => $groups
            ]);

        } catch (\Exception $e) {
            error_log('Error getting user groups: ' . $e->getMessage());
            $this->sendJsonResponse([
                'error' => 'Възникна грешка при зареждане на групите'
            ]);
        }
    }

    /**
     * Валидация на данните за потребител
     */
    private function validateUserData($data, $userId = null) {
        $errors = [];

        // Валидация на потребителско име
        if (empty($data['username'])) {
            $errors['username'] = 'Потребителското име е задължително';
        } elseif (strlen($data['username']) < 3) {
            $errors['username'] = 'Потребителското име трябва да бъде поне 3 символа';
        } else {
            // Проверка за уникалност
            $existingUser = $this->adminUsersModel->getUserByUsername($data['username']);
            if ($existingUser && (!$userId || $existingUser['user_id'] != $userId)) {
                $errors['username'] = 'Това потребителско име вече се използва';
            }
        }

        // Валидация на email
        if (empty($data['email'])) {
            $errors['email'] = 'Email адресът е задължителен';
        } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Невалиден email адрес';
        } else {
            // Проверка за уникалност
            $existingUser = $this->adminUsersModel->getUserByEmail($data['email']);
            if ($existingUser && (!$userId || $existingUser['user_id'] != $userId)) {
                $errors['email'] = 'Този email адрес вече се използва';
            }
        }

        // Валидация на име и фамилия
        if (empty($data['firstname'])) {
            $errors['firstname'] = 'Името е задължително';
        }

        if (empty($data['lastname'])) {
            $errors['lastname'] = 'Фамилията е задължителна';
        }

        // Валидация на парола (само при добавяне или ако е въведена)
        if (!$userId || !empty($data['password'])) {
            if (empty($data['password'])) {
                $errors['password'] = 'Паролата е задължителна';
            } elseif (strlen($data['password']) < 6) {
                $errors['password'] = 'Паролата трябва да бъде поне 6 символа';
            } elseif ($data['password'] !== $data['confirm_password']) {
                $errors['confirm_password'] = 'Паролите не съвпадат';
            }
        }

        // Валидация на потребителска група
        if (empty($data['user_group_id'])) {
            $errors['user_group_id'] = 'Потребителската група е задължителна';
        }

        return $errors;
    }

    /**
     * Подготовка на данните за запазване
     */
    private function prepareUserDataForSave($data) {
        $userData = [
            'username' => trim($data['username']),
            'email' => trim($data['email']),
            'firstname' => trim($data['firstname']),
            'lastname' => trim($data['lastname']),
            'user_group_id' => (int)$data['user_group_id'],
            'status' => isset($data['status']) ? (int)$data['status'] : 1
        ];

        // Добавяне на парола само ако е въведена
        if (!empty($data['password'])) {
            $userData['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        return $userData;
    }

    /**
     * Проверка за AJAX заявка
     */
    private function checkAjaxRequest() {
        if (!$this->request->isAjax()) {
            $this->sendJsonResponse(['error' => 'Невалидна заявка']);
            exit;
        }

        if (!$this->user->hasPermission('modify', 'setting/admin_users')) {
            $this->sendJsonResponse(['error' => 'Нямате права за тази операция']);
            exit;
        }
    }

    /**
     * Изпращане на JSON отговор
     */
    private function sendJsonResponse($data) {
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }
}
