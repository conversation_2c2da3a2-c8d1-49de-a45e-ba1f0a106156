

<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Настройки за интеграции</h1>
    <p class="text-gray-600 mt-1">Управлявайте API интеграции, analytics tools, social media и marketplace връзки</p>
</div>

<form id="integration-settings-form" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Analytics Integrations -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-bar-chart-line mr-2"></i>
                    Analytics интеграции
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% for analytics_code, analytics in analytics_integrations %}
                    <div class="integration-card border border-gray-200 rounded p-4 {{ analytics.status ? 'border-green-200 bg-green-50' : 'opacity-50' }}">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-white border flex items-center justify-center mr-3">
                                    <i class="{{ analytics.icon }} text-lg {{ analytics.color }}"></i>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">{{ analytics.name }}</h3>
                                    <p class="text-xs text-gray-500">{{ analytics.description }}</p>
                                </div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       name="analytics_integrations[{{ analytics_code }}][status]"
                                       class="integration-toggle"
                                       data-integration="{{ analytics_code }}"
                                       data-type="analytics"
                                       {{ analytics.status ? 'checked' : '' }}>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="space-y-3">
                            {% if analytics_code == 'google_analytics' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Tracking ID
                                    </label>
                                    <input type="text" 
                                           name="analytics_integrations[{{ analytics_code }}][tracking_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ analytics.tracking_id ?? '' }}"
                                           placeholder="UA-XXXXXXXX-X или G-XXXXXXXXXX">
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">Enhanced Ecommerce</span>
                                    <input type="checkbox" 
                                           name="analytics_integrations[{{ analytics_code }}][enhanced_ecommerce]"
                                           class="toggle-switch"
                                           {{ analytics.enhanced_ecommerce ? 'checked' : '' }}>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">Anonymize IP</span>
                                    <input type="checkbox" 
                                           name="analytics_integrations[{{ analytics_code }}][anonymize_ip]"
                                           class="toggle-switch"
                                           {{ analytics.anonymize_ip ? 'checked' : '' }}>
                                </div>
                            {% elseif analytics_code == 'google_tag_manager' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Container ID
                                    </label>
                                    <input type="text" 
                                           name="analytics_integrations[{{ analytics_code }}][container_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ analytics.container_id ?? '' }}"
                                           placeholder="GTM-XXXXXXX">
                                </div>
                            {% elseif analytics_code == 'facebook_pixel' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Pixel ID
                                    </label>
                                    <input type="text" 
                                           name="analytics_integrations[{{ analytics_code }}][pixel_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ analytics.pixel_id ?? '' }}"
                                           placeholder="123456789012345">
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">Advanced Matching</span>
                                    <input type="checkbox" 
                                           name="analytics_integrations[{{ analytics_code }}][advanced_matching]"
                                           class="toggle-switch"
                                           {{ analytics.advanced_matching ? 'checked' : '' }}>
                                </div>
                            {% elseif analytics_code == 'hotjar' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Site ID
                                    </label>
                                    <input type="text" 
                                           name="analytics_integrations[{{ analytics_code }}][site_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ analytics.site_id ?? '' }}"
                                           placeholder="1234567">
                                </div>
                            {% endif %}
                            
                            <div class="flex justify-end">
                                <button type="button" 
                                        data-integration="{{ analytics_code }}"
                                        data-type="analytics"
                                        class="test-integration px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600">
                                    <i class="ri-test-tube-line mr-1"></i>
                                    Тест
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Social Media Integrations -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-share-line mr-2"></i>
                    Social Media интеграции
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% for social_code, social in social_integrations %}
                    <div class="integration-card border border-gray-200 rounded p-4 {{ social.status ? 'border-green-200 bg-green-50' : 'opacity-50' }}">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-white border flex items-center justify-center mr-3">
                                    <i class="{{ social.icon }} text-lg {{ social.color }}"></i>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">{{ social.name }}</h3>
                                    <p class="text-xs text-gray-500">{{ social.description }}</p>
                                </div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       name="social_integrations[{{ social_code }}][status]"
                                       class="integration-toggle"
                                       data-integration="{{ social_code }}"
                                       data-type="social"
                                       {{ social.status ? 'checked' : '' }}>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="space-y-3">
                            {% if social_code == 'facebook_login' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        App ID
                                    </label>
                                    <input type="text" 
                                           name="social_integrations[{{ social_code }}][app_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ social.app_id ?? '' }}"
                                           placeholder="123456789012345">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        App Secret
                                    </label>
                                    <input type="password" 
                                           name="social_integrations[{{ social_code }}][app_secret]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ social.app_secret ?? '' }}"
                                           placeholder="••••••••••••••••">
                                </div>
                            {% elseif social_code == 'google_login' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Client ID
                                    </label>
                                    <input type="text" 
                                           name="social_integrations[{{ social_code }}][client_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ social.client_id ?? '' }}"
                                           placeholder="123456789012-abc...">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Client Secret
                                    </label>
                                    <input type="password" 
                                           name="social_integrations[{{ social_code }}][client_secret]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ social.client_secret ?? '' }}"
                                           placeholder="••••••••••••••••">
                                </div>
                            {% elseif social_code == 'instagram_feed' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Access Token
                                    </label>
                                    <input type="password" 
                                           name="social_integrations[{{ social_code }}][access_token]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ social.access_token ?? '' }}"
                                           placeholder="••••••••••••••••">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        User ID
                                    </label>
                                    <input type="text" 
                                           name="social_integrations[{{ social_code }}][user_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ social.user_id ?? '' }}"
                                           placeholder="123456789">
                                </div>
                            {% elseif social_code == 'youtube_channel' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Channel ID
                                    </label>
                                    <input type="text" 
                                           name="social_integrations[{{ social_code }}][channel_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ social.channel_id ?? '' }}"
                                           placeholder="UCxxxxxxxxxxxxxxxxxxxxxxx">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        API Key
                                    </label>
                                    <input type="password" 
                                           name="social_integrations[{{ social_code }}][api_key]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ social.api_key ?? '' }}"
                                           placeholder="••••••••••••••••">
                                </div>
                            {% endif %}
                            
                            <div class="flex justify-end">
                                <button type="button" 
                                        data-integration="{{ social_code }}"
                                        data-type="social"
                                        class="test-integration px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600">
                                    <i class="ri-test-tube-line mr-1"></i>
                                    Тест
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- API Integrations -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-code-line mr-2"></i>
                    API интеграции
                </h2>
                
                <div class="space-y-4">
                    {% for api_code, api in api_integrations %}
                    <div class="integration-card border border-gray-200 rounded p-4 {{ api.status ? 'border-green-200 bg-green-50' : 'opacity-50' }}">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-white border flex items-center justify-center mr-3">
                                    <i class="{{ api.icon }} text-lg {{ api.color }}"></i>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">{{ api.name }}</h3>
                                    <p class="text-xs text-gray-500">{{ api.description }}</p>
                                </div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       name="api_integrations[{{ api_code }}][status]"
                                       class="integration-toggle"
                                       data-integration="{{ api_code }}"
                                       data-type="api"
                                       {{ api.status ? 'checked' : '' }}>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    API Key
                                </label>
                                <div class="flex">
                                    <input type="password" 
                                           name="api_integrations[{{ api_code }}][api_key]"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-l text-sm"
                                           value="{{ api.api_key ?? '' }}"
                                           placeholder="••••••••••••••••">
                                    <button type="button" 
                                            data-api="{{ api_code }}"
                                            class="generate-api-key px-3 py-2 bg-gray-500 text-white text-sm rounded-r hover:bg-gray-600">
                                        <i class="ri-key-line"></i>
                                    </button>
                                </div>
                            </div>
                            
                            {% if api_code != 'opencart_api' %}
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Secret Key
                                </label>
                                <input type="password" 
                                       name="api_integrations[{{ api_code }}][secret_key]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                       value="{{ api.secret_key ?? '' }}"
                                       placeholder="••••••••••••••••">
                            </div>
                            {% endif %}
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Rate Limit (заявки/час)
                                </label>
                                <input type="number" 
                                       name="api_integrations[{{ api_code }}][rate_limit]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                       value="{{ api.rate_limit ?? 1000 }}"
                                       min="1"
                                       max="10000">
                            </div>
                            
                            {% if api_code == 'webhook_api' %}
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Timeout (секунди)
                                </label>
                                <input type="number" 
                                       name="api_integrations[{{ api_code }}][timeout]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                       value="{{ api.timeout ?? 30 }}"
                                       min="5"
                                       max="300">
                            </div>
                            {% endif %}
                            
                            {% if api_code == 'opencart_api' %}
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Разрешени IP адреси
                                </label>
                                <input type="text" 
                                       name="api_integrations[{{ api_code }}][allowed_ips]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                       value="{{ api.allowed_ips ?? '' }}"
                                       placeholder="***********, ********">
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="flex justify-end mt-3">
                            <button type="button" 
                                    data-integration="{{ api_code }}"
                                    data-type="api"
                                    class="test-integration px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600">
                                <i class="ri-test-tube-line mr-1"></i>
                                Тест
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Marketplace Integrations -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-store-line mr-2"></i>
                    Marketplace интеграции
                </h2>

                <div class="space-y-4">
                    {% for marketplace_code, marketplace in marketplaces %}
                    <div class="integration-card border border-gray-200 rounded p-4 {{ marketplace.status ? 'border-green-200 bg-green-50' : 'opacity-50' }}">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-white border flex items-center justify-center mr-3">
                                    <i class="{{ marketplace.icon }} text-lg {{ marketplace.color }}"></i>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">{{ marketplace.name }}</h3>
                                    <p class="text-xs text-gray-500">{{ marketplace.description }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button type="button"
                                        data-marketplace="{{ marketplace_code }}"
                                        class="sync-marketplace px-3 py-1 bg-orange-500 text-white text-sm rounded hover:bg-orange-600">
                                    <i class="ri-refresh-line mr-1"></i>
                                    Синхронизирай
                                </button>
                                <label class="toggle-switch">
                                    <input type="checkbox"
                                           name="marketplaces[{{ marketplace_code }}][status]"
                                           class="integration-toggle"
                                           data-integration="{{ marketplace_code }}"
                                           data-type="marketplace"
                                           {{ marketplace.status ? 'checked' : '' }}>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            {% if marketplace_code == 'amazon' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Seller ID
                                    </label>
                                    <input type="text"
                                           name="marketplaces[{{ marketplace_code }}][seller_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.seller_id ?? '' }}"
                                           placeholder="A1XXXXXXXXXX">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Marketplace ID
                                    </label>
                                    <input type="text"
                                           name="marketplaces[{{ marketplace_code }}][marketplace_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.marketplace_id ?? '' }}"
                                           placeholder="ATVPDKIKX0DER">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Access Key
                                    </label>
                                    <input type="password"
                                           name="marketplaces[{{ marketplace_code }}][access_key]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.access_key ?? '' }}"
                                           placeholder="••••••••••••••••">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Secret Key
                                    </label>
                                    <input type="password"
                                           name="marketplaces[{{ marketplace_code }}][secret_key]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.secret_key ?? '' }}"
                                           placeholder="••••••••••••••••">
                                </div>
                            {% elseif marketplace_code == 'ebay' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        App ID
                                    </label>
                                    <input type="text"
                                           name="marketplaces[{{ marketplace_code }}][app_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.app_id ?? '' }}"
                                           placeholder="YourAppI-xxxx-xxxx-xxxx-xxxxxxxxxxxx">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Dev ID
                                    </label>
                                    <input type="text"
                                           name="marketplaces[{{ marketplace_code }}][dev_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.dev_id ?? '' }}"
                                           placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Cert ID
                                    </label>
                                    <input type="password"
                                           name="marketplaces[{{ marketplace_code }}][cert_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.cert_id ?? '' }}"
                                           placeholder="••••••••••••••••">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        User Token
                                    </label>
                                    <input type="password"
                                           name="marketplaces[{{ marketplace_code }}][token]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.token ?? '' }}"
                                           placeholder="••••••••••••••••">
                                </div>
                            {% elseif marketplace_code == 'etsy' %}
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        API Key
                                    </label>
                                    <input type="password"
                                           name="marketplaces[{{ marketplace_code }}][api_key]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.api_key ?? '' }}"
                                           placeholder="••••••••••••••••">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Shared Secret
                                    </label>
                                    <input type="password"
                                           name="marketplaces[{{ marketplace_code }}][shared_secret]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.shared_secret ?? '' }}"
                                           placeholder="••••••••••••••••">
                                </div>

                                <div class="col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Shop ID
                                    </label>
                                    <input type="text"
                                           name="marketplaces[{{ marketplace_code }}][shop_id]"
                                           class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                           value="{{ marketplace.shop_id ?? '' }}"
                                           placeholder="12345678">
                                </div>
                            {% endif %}
                        </div>

                        <div class="flex justify-end mt-3">
                            <button type="button"
                                    data-integration="{{ marketplace_code }}"
                                    data-type="marketplace"
                                    class="test-integration px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600">
                                <i class="ri-test-tube-line mr-1"></i>
                                Тест
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Webhooks -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">
                        <i class="ri-webhook-line mr-2"></i>
                        Webhooks
                    </h2>
                    <button type="button"
                            id="add-webhook"
                            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        <i class="ri-add-line mr-2"></i>
                        Добави Webhook
                    </button>
                </div>
                
                <div id="webhooks-container" class="space-y-4">
                    {% for webhook in webhooks %}
                    <div id="webhook-{{ loop.index0 }}" class="webhook-item border border-gray-200 rounded p-4">
                        <div class="grid grid-cols-2 gap-4 mb-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Име на webhook
                                </label>
                                <input type="text" 
                                       name="webhooks[{{ loop.index0 }}][name]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                       value="{{ webhook.name ?? '' }}"
                                       placeholder="Име на webhook...">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Събитие
                                </label>
                                <select name="webhooks[{{ loop.index0 }}][event]"
                                        class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                    <option value="order.created" {{ webhook.event == 'order.created' ? 'selected' : '' }}>Нова поръчка</option>
                                    <option value="order.status_changed" {{ webhook.event == 'order.status_changed' ? 'selected' : '' }}>Промяна в статус</option>
                                    <option value="customer.created" {{ webhook.event == 'customer.created' ? 'selected' : '' }}>Нов клиент</option>
                                    <option value="product.updated" {{ webhook.event == 'product.updated' ? 'selected' : '' }}>Актуализиран продукт</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-4 mb-3">
                            <div class="col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    URL
                                </label>
                                <input type="url" 
                                       name="webhooks[{{ loop.index0 }}][url]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                       value="{{ webhook.url ?? '' }}"
                                       placeholder="https://example.com/webhook">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Метод
                                </label>
                                <select name="webhooks[{{ loop.index0 }}][method]"
                                        class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                    <option value="POST" {{ webhook.method == 'POST' ? 'selected' : '' }}>POST</option>
                                    <option value="PUT" {{ webhook.method == 'PUT' ? 'selected' : '' }}>PUT</option>
                                    <option value="PATCH" {{ webhook.method == 'PATCH' ? 'selected' : '' }}>PATCH</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox" 
                                       name="webhooks[{{ loop.index0 }}][status]"
                                       class="toggle-switch"
                                       {{ webhook.status ? 'checked' : '' }}>
                                <span class="ml-2 text-sm text-gray-700">Активен</span>
                            </label>
                            
                            <div class="flex space-x-2">
                                <button type="button" 
                                        data-index="{{ loop.index0 }}"
                                        class="test-webhook px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600">
                                    <i class="ri-test-tube-line mr-1"></i>
                                    Тест
                                </button>
                                <button type="button" 
                                        data-index="{{ loop.index0 }}"
                                        class="remove-webhook px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600">
                                    <i class="ri-delete-bin-line mr-1"></i>
                                    Премахни
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">


            <!-- Quick Actions -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-tools-line mr-2"></i>
                    Бързи действия
                </h2>
                
                <div class="space-y-3">
                    <button type="button" 
                            id="save-integration-settings"
                            class="w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors">
                        <i class="ri-save-line mr-2"></i>
                        Запази настройки
                    </button>

                    <button type="button" 
                            onclick="location.reload()"
                            class="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        <i class="ri-refresh-line mr-2"></i>
                        Презареди
                    </button>
                    

                </div>
            </div>

            <!-- Integration Tips -->
            <div class="bg-blue-50 border border-blue-200 rounded p-6 mt-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="ri-lightbulb-line mr-2"></i>
                    Съвети за интеграции
                </h2>
                
                <div class="space-y-3 text-sm text-blue-700">
                    <div class="flex items-start">
                        <i class="ri-check-line text-blue-500 mr-2 mt-0.5"></i>
                        <span>Тествайте интеграциите преди да ги активирате</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-blue-500 mr-2 mt-0.5"></i>
                        <span>Използвайте HTTPS за всички webhook URL-и</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-blue-500 mr-2 mt-0.5"></i>
                        <span>Ограничете API rate limits според нуждите</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-blue-500 mr-2 mt-0.5"></i>
                        <span>Редовно проверявайте API ключовете за сигурност</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

{% if tab_script_url %}
<script type="text/javascript" src="{{ tab_script_url }}"></script>
{% endif %}
