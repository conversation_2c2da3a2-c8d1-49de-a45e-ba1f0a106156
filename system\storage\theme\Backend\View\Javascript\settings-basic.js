/**
 * JavaScript модул за основни настройки
 * Следва BackendModule pattern на темата Rakla.bg
 * Разширява основния settings модул
 */
(function() {
    'use strict';

    // Функция за инициализация на basic модула
    function initBasicModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModuleWithBasic();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул с функционалности за основни настройки
    function extendBackendModuleWithBasic() {
        Object.assign(BackendModule, {
            /**
             * Инициализиране на основни настройки
             */
            initBasicSettings: function() {
                this.bindBasicSettingsEvents();
                this.logDev && this.logDev('Basic settings module initialized');
            },

            /**
             * Свързване на събития за основни настройки
             */
            bindBasicSettingsEvents: function() {
                const self = this;

                // Форма за основни настройки
                const basicForm = document.getElementById('basic-settings-form');
                if (basicForm) {
                    basicForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveBasicSettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-basic-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveBasicSettings();
                    });
                }

                // Качване на лого
                const logoUpload = document.getElementById('logo-upload');
                if (logoUpload) {
                    logoUpload.addEventListener('change', function(e) {
                        self.handleLogoUpload(e);
                    });
                }

                // Тест на email
                const testEmailButton = document.getElementById('test-email');
                if (testEmailButton) {
                    testEmailButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testEmail();
                    });
                }
            },

            /**
             * Обработка на click събития за basic таб
             */
            handleBasicClick: function(e) {
                const target = e.target;
                const self = this;

                if (target.id === 'save-basic-settings') {
                    e.preventDefault();
                    self.saveBasicSettings();
                } else if (target.id === 'test-email') {
                    e.preventDefault();
                    self.testEmail();
                }
            },

            /**
             * Обработка на form submit събития за basic таб
             */
            handleBasicFormSubmit: function(e) {
                e.preventDefault();
                const form = e.target;
                const self = this;

                if (form.id === 'basic-settings-form') {
                    self.saveBasicSettings();
                }
            },

            /**
             * Обработка на change събития за basic таб
             */
            handleBasicChange: function(e) {
                const target = e.target;
                const self = this;

                // Автоматично запазване при промяна на checkbox-и
                if (target.type === 'checkbox' && target.name && target.name.includes('setting')) {
                    self.autoSaveSettings();
                }

                // Обработка на качване на лого
                if (target.id === 'logo-upload') {
                    self.handleLogoUpload(e);
                }
            },

            /**
             * Запазване на основни настройки
             */
            saveBasicSettings: function() {
                const self = this;
                const form = document.getElementById('basic-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-basic-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.basic_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateBasicSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving basic settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази настройките';
                    }
                });
            },

            /**
             * Обработка на качване на лого
             */
            handleLogoUpload: function(e) {
                const self = this;
                const file = e.target.files[0];

                if (!file) {
                    return;
                }

                // Валидация на файла
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    self.showSettingsNotification('Моля, изберете валиден файл с изображение (JPG, PNG, GIF)', 'error');
                    e.target.value = '';
                    return;
                }

                const maxSize = 2 * 1024 * 1024; // 2MB
                if (file.size > maxSize) {
                    self.showSettingsNotification('Файлът е твърде голям. Максималният размер е 2MB', 'error');
                    e.target.value = '';
                    return;
                }

                // Показване на preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    const logoPreview = document.getElementById('logo-preview');
                    if (logoPreview) {
                        logoPreview.src = e.target.result;
                        logoPreview.style.display = 'block';
                    }
                };
                reader.readAsDataURL(file);

                self.showSettingsNotification('Логото е избрано. Запазете настройките за да се приложи промяната.', 'info');
            },

            /**
             * Тестване на email настройки
             */
            testEmail: function() {
                const self = this;
                const testEmailButton = document.getElementById('test-email');

                if (testEmailButton) {
                    testEmailButton.disabled = true;
                    testEmailButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Изпращане...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                fetch(self.settings.config.ajaxUrls.test_email || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'Тестовият email е изпратен успешно', 'success');
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при изпращане на тестовия email', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing email:', error);
                    self.showSettingsNotification('Възникна грешка при тестването на email', 'error');
                })
                .finally(() => {
                    if (testEmailButton) {
                        testEmailButton.disabled = false;
                        testEmailButton.innerHTML = '<i class="ri-mail-line mr-2"></i>Тест на email';
                    }
                });
            },

            /**
             * Актуализиране на показването на основни настройки
             */
            updateBasicSettingsDisplay: function(data) {
                // Актуализиране на полетата с новите стойности
                Object.keys(data).forEach(key => {
                    const field = document.querySelector(`[name="${key}"]`);
                    if (field) {
                        if (field.type === 'checkbox') {
                            field.checked = !!data[key];
                        } else {
                            field.value = data[key] || '';
                        }
                    }
                });

                // Актуализиране на логото ако е променено
                if (data.logo_url) {
                    const logoPreview = document.getElementById('logo-preview');
                    if (logoPreview) {
                        logoPreview.src = data.logo_url;
                        logoPreview.style.display = 'block';
                    }
                }

                // Актуализиране на статистики ако има
                if (data.statistics) {
                    Object.keys(data.statistics).forEach(statKey => {
                        const statElement = document.getElementById(statKey + '-count');
                        if (statElement) {
                            statElement.textContent = data.statistics[statKey];
                        }
                    });
                }
            },

            /**
             * Показване на грешки при валидация
             */
            showValidationErrors: function(errors) {
                const self = this;
                
                if (Array.isArray(errors)) {
                    errors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                } else if (typeof errors === 'object') {
                    Object.keys(errors).forEach(field => {
                        const fieldElement = document.querySelector(`[name="${field}"]`);
                        if (fieldElement) {
                            fieldElement.classList.add('border-red-500');
                            
                            // Премахване на грешката при промяна на полето
                            fieldElement.addEventListener('input', function() {
                                this.classList.remove('border-red-500');
                            }, { once: true });
                        }
                        
                        self.showSettingsNotification(errors[field], 'error');
                    });
                }
            }
        });
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initBasicModule();
    });

})();
