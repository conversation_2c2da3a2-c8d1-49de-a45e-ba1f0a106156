/**
 * JavaScript модул за настройки на админ потребители
 * Следва BackendModule pattern на темата Rakla.bg
 * Разширява основния settings модул
 */
(function() {
    'use strict';

    // Функция за инициализация на admin users модула
    function initAdminUsersModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModuleWithAdminUsers();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул с функционалности за админ потребители
    function extendBackendModuleWithAdminUsers() {
        Object.assign(BackendModule, {
            /**
             * Инициализиране на настройки за админ потребители
             */
            initAdminUsersSettings: function() {
                this.bindAdminUsersEvents();
                this.initUserTable();
                this.initUserGroupsTable();
                this.logDev && this.logDev('Admin users settings module initialized');
            },

            /**
             * Свързване на събития за настройки за админ потребители
             */
            bindAdminUsersEvents: function() {
                const self = this;

                // Забележка: Събитията сега се обработват чрез event delegation
                // в handleTabClick, handleTabFormSubmit и handleTabChange функциите
                // Това осигурява правилно функциониране дори когато съдържанието
                // на таба се зарежда динамично чрез AJAX

                self.logDev && self.logDev('Admin users events will be handled via event delegation');
            },

            /**
             * Обработка на click събития за admin users таб
             */
            handleAdminUsersClick: function(e) {
                const target = e.target;
                const self = this;

                // Проверяваме за специфични класове и ID-та
                if (target.id === 'add-admin-user' || target.classList.contains('add-admin-user')) {
                    e.preventDefault();
                    self.showAddUserModal();
                } else if (target.id === 'add-user-group' || target.classList.contains('add-user-group')) {
                    e.preventDefault();
                    self.showAddUserGroupModal();
                } else if (target.classList.contains('edit-user')) {
                    e.preventDefault();
                    const userId = target.getAttribute('data-user-id');
                    self.editUser(userId);
                } else if (target.classList.contains('delete-user')) {
                    e.preventDefault();
                    const userId = target.getAttribute('data-user-id');
                    self.deleteUser(userId);
                } else if (target.classList.contains('toggle-user-status')) {
                    e.preventDefault();
                    const userId = target.getAttribute('data-user-id');
                    self.toggleUserStatus(userId);
                } else if (target.classList.contains('reset-user-password')) {
                    e.preventDefault();
                    const userId = target.getAttribute('data-user-id');
                    self.resetUserPassword(userId);
                } else if (target.classList.contains('unlock-user')) {
                    e.preventDefault();
                    const userId = target.getAttribute('data-user-id');
                    self.unlockUser(userId);
                } else if (target.classList.contains('edit-user-group')) {
                    e.preventDefault();
                    const groupId = target.getAttribute('data-group-id');
                    self.editUserGroup(groupId);
                } else if (target.classList.contains('delete-user-group')) {
                    e.preventDefault();
                    const groupId = target.getAttribute('data-group-id');
                    self.deleteUserGroup(groupId);
                } else if (target.classList.contains('manage-permissions')) {
                    e.preventDefault();
                    const groupId = target.getAttribute('data-group-id');
                    self.managePermissions(groupId);
                } else if (target.id === 'save-admin-users-settings') {
                    e.preventDefault();
                    self.saveAdminUsersSettings();
                }
            },

            /**
             * Обработка на form submit събития за admin users таб
             */
            handleAdminUsersFormSubmit: function(e) {
                e.preventDefault();
                const form = e.target;
                const self = this;

                if (form.id === 'add-user-form') {
                    self.submitAddUserForm(form);
                } else if (form.id === 'add-group-form') {
                    self.submitAddGroupForm(form);
                } else if (form.id === 'edit-user-form') {
                    self.submitEditUserForm(form);
                } else if (form.id === 'edit-group-form') {
                    // TODO: Implement edit group form submission
                    self.logDev && self.logDev('Edit group form submission not yet implemented');
                } else if (form.id === 'admin-users-settings-form') {
                    self.saveAdminUsersSettings();
                }
            },

            /**
             * Обработка на change събития за admin users таб
             */
            handleAdminUsersChange: function(e) {
                const target = e.target;
                const self = this;

                // Автоматично запазване при промяна на checkbox-и
                if (target.type === 'checkbox' && target.name && target.name.includes('setting')) {
                    self.autoSaveSettings();
                }

                // IP whitelist toggle
                if (target.id === 'ip_whitelist_enabled') {
                    self.toggleIPWhitelistFields(target.checked);
                }
            },

            /**
             * Запазване на настройки за админ потребители
             */
            saveAdminUsersSettings: function() {
                const self = this;
                const form = document.getElementById('admin-users-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateAdminUsersForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-admin-users-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.admin_users_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateAdminUsersSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving admin users settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за админ потребители
             */
            validateAdminUsersForm: function(form) {
                const errors = [];

                // Валидация на настройки за сигурност
                const minLength = form.querySelector('[name="security_settings[password_min_length]"]')?.value;
                if (minLength && (parseInt(minLength) < 6 || parseInt(minLength) > 32)) {
                    errors.push('Минималната дължина на паролата трябва да бъде между 6 и 32 символа');
                }

                const maxAttempts = form.querySelector('[name="security_settings[max_login_attempts]"]')?.value;
                if (maxAttempts && (parseInt(maxAttempts) < 3 || parseInt(maxAttempts) > 20)) {
                    errors.push('Максималните опити за вход трябва да бъдат между 3 и 20');
                }

                const sessionTimeout = form.querySelector('[name="security_settings[session_timeout]"]')?.value;
                if (sessionTimeout && (parseInt(sessionTimeout) < 300 || parseInt(sessionTimeout) > 86400)) {
                    errors.push('Timeout на сесията трябва да бъде между 300 и 86400 секунди');
                }

                // Валидация на IP whitelist ако е активиран
                const ipWhitelistEnabled = form.querySelector('[name="security_settings[ip_whitelist_enabled]"]')?.checked;
                const ipWhitelist = form.querySelector('[name="security_settings[ip_whitelist]"]')?.value;

                if (ipWhitelistEnabled && !ipWhitelist) {
                    errors.push('IP whitelist е активиран, но няма зададени IP адреси');
                } else if (ipWhitelist) {
                    const ips = ipWhitelist.split(',');
                    ips.forEach(ip => {
                        const trimmedIp = ip.trim();
                        if (trimmedIp && !self.isValidIP(trimmedIp)) {
                            errors.push(`Невалиден IP адрес: ${trimmedIp}`);
                        }
                    });
                }

                return errors;
            },

            /**
             * Проверка за валиден IP адрес
             */
            isValidIP: function(ip) {
                const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
                return ipRegex.test(ip);
            },

            /**
             * Показване на modal за добавяне на потребител
             */
            showAddUserModal: function() {
                const self = this;

                // Създаване на modal за добавяне на потребител
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на потребител</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-user-form" class="p-4">
                            <div class="space-y-4">
                                <div>
                                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Потребителско име</label>
                                    <input type="text" id="username" name="username" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Парола</label>
                                    <input type="password" id="password" name="password" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input type="email" id="email" name="email" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="firstname" class="block text-sm font-medium text-gray-700 mb-1">Име</label>
                                    <input type="text" id="firstname" name="firstname" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="lastname" class="block text-sm font-medium text-gray-700 mb-1">Фамилия</label>
                                    <input type="text" id="lastname" name="lastname" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="user_group_id" class="block text-sm font-medium text-gray-700 mb-1">Група</label>
                                    <select id="user_group_id" name="user_group_id" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                        <option value="">Изберете група</option>
                                    </select>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="status" name="status" value="1" checked
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                    <label for="status" class="ml-2 block text-sm text-gray-900">Активен</label>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                    Отказ
                                </button>
                                <button type="submit" id="save-new-user" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                    <i class="ri-save-line mr-2"></i>Запази
                                </button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(modal);

                // Зареждане на потребителски групи
                const userGroupSelect = modal.querySelector('#user_group_id');
                self.loadUserGroups(userGroupSelect);
            },

            /**
             * Показване на modal за добавяне на група потребители
             */
            showAddUserGroupModal: function() {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на група</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-group-form" class="p-4">
                            <div class="space-y-4">
                                <div>
                                    <label for="group_name" class="block text-sm font-medium text-gray-700 mb-1">Име на групата</label>
                                    <input type="text" id="group_name" name="name" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="group_description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
                                    <textarea id="group_description" name="description" rows="3"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"></textarea>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                    Отказ
                                </button>
                                <button type="submit" id="save-new-group" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                    <i class="ri-save-line mr-2"></i>Запази
                                </button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(modal);
            },

            /**
             * Редактиране на потребител
             */
            editUser: function(userId) {
                const self = this;

                // Първо зареждаме данните за потребителя
                const getUserUrl = self.settings.config.ajaxUrls.get_user ||
                                  window.location.pathname + '?route=setting/admin_users/get_user&user_token=' + self.settings.config.userToken + '&user_id=' + userId;

                fetch(getUserUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.user) {
                        self.showEditUserModal(data.user);
                    } else {
                        self.showSettingsNotification('Грешка при зареждане на данните за потребителя', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error loading user data:', error);
                    self.showSettingsNotification('Грешка при зареждане на данните за потребителя', 'error');
                });
            },

            /**
             * Изтриване на потребител
             */
            deleteUser: function(userId) {
                const self = this;

                // Показване на confirmation modal
                self.showDeleteConfirmModal('потребител', () => {
                    const formData = new FormData();
                    formData.append('user_token', self.settings.config.userToken);
                    formData.append('user_id', userId);

                    const deleteUserUrl = self.settings.config.ajaxUrls.delete_user ||
                                         window.location.pathname + '?route=setting/admin_users/delete&user_token=' + self.settings.config.userToken;

                    fetch(deleteUserUrl, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            self.showSettingsNotification(data.success, 'success');

                            // Презареждане на таблицата с потребители
                            self.refreshUsersTable();
                        } else if (data.error) {
                            self.showSettingsNotification(data.error, 'error');
                        }
                    })
                    .catch(error => {
                        self.logDev && self.logDev('Error deleting user:', error);
                        self.showSettingsNotification('Възникна грешка при изтриването', 'error');
                    });
                });
            },

            /**
             * Инициализиране на таблицата с потребители
             */
            initUserTable: function() {
                // Инициализиране на функционалност за таблицата с потребители
                this.logDev && this.logDev('User table initialized');
            },

            /**
             * Инициализиране на таблицата с групи потребители
             */
            initUserGroupsTable: function() {
                // Инициализиране на функционалност за таблицата с групи
                this.logDev && this.logDev('User groups table initialized');
            },

            /**
             * Зареждане на потребителски групи в select поле
             */
            loadUserGroups: function(selectElement, selectedValue = null) {
                const self = this;

                if (!selectElement) {
                    self.logDev && self.logDev('Select element not found for user groups');
                    return;
                }

                // Показване на loading състояние
                selectElement.innerHTML = '<option value="">Зареждане...</option>';

                const getUserGroupsUrl = self.settings.config.ajaxUrls.get_user_groups ||
                                       window.location.pathname + '?route=setting/admin_users/get_user_groups&user_token=' + self.settings.config.userToken;

                fetch(getUserGroupsUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    selectElement.innerHTML = '';

                    if (data.user_groups && Array.isArray(data.user_groups)) {
                        data.user_groups.forEach(group => {
                            const option = document.createElement('option');
                            option.value = group.user_group_id;
                            option.textContent = group.name;
                            if (selectedValue && group.user_group_id == selectedValue) {
                                option.selected = true;
                            }
                            selectElement.appendChild(option);
                        });
                    } else {
                        selectElement.innerHTML = '<option value="">Няма налични групи</option>';
                    }
                })
                .catch(error => {
                    console.error('Error loading user groups:', error);
                    selectElement.innerHTML = '<option value="">Грешка при зареждане</option>';
                });
            },

            /**
             * Показване на modal за добавяне на потребител
             */
            showAddUserModal: function() {
                const self = this;

                // Създаване на modal за добавяне на потребител
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на потребител</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-user-form" class="p-4 space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Потребителско име <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="username" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="Въведете потребителско име">
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Име <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="firstname" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded"
                                           placeholder="Име">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Фамилия <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="lastname" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded"
                                           placeholder="Фамилия">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Email <span class="text-red-500">*</span>
                                </label>
                                <input type="email" name="email" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="<EMAIL>">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Парола <span class="text-red-500">*</span>
                                </label>
                                <input type="password" name="password" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="Въведете парола">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Група потребители
                                </label>
                                <select name="user_group_id" required id="user-group-select"
                                        class="w-full px-3 py-2 border border-gray-300 rounded">
                                    <option value="">Зареждане...</option>
                                </select>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" name="status" id="user-status" class="mr-2">
                                <label for="user-status" class="text-sm text-gray-700">Активен</label>
                            </div>
                        </form>
                        <div class="flex justify-end p-4 border-t space-x-2">
                            <button class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Отказ
                            </button>
                            <button id="save-new-user" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                Запази
                            </button>
                        </div>
                    </div>
                `;

                // Добавяне на modal към страницата
                document.body.appendChild(modal);

                // Зареждане на потребителските групи
                this.loadUserGroups(modal.querySelector('#user-group-select'));

                // Фокус на първото поле
                const firstInput = modal.querySelector('input[name="username"]');
                if (firstInput) {
                    firstInput.focus();
                }
            }
        });
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initAdminUsersModule();
    });

})();
