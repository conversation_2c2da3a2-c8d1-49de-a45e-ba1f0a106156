/**
 * JavaScript модул за настройки за сигурност
 * Следва BackendModule pattern на темата Rakla.bg
 * Разширява основния settings модул
 */
(function() {
    'use strict';

    // Функция за инициализация на security модула
    function initSecurityModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModuleWithSecurity();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул с функционалности за сигурност
    function extendBackendModuleWithSecurity() {
        Object.assign(BackendModule, {
            /**
             * Инициализиране на настройки за сигурност
             */
            initSecuritySettings: function() {
                this.bindSecuritySettingsEvents();
                this.initIPAddressesTable();
                this.bindIPWhitelistToggle();
                this.logDev && this.logDev('Security settings module initialized');
            },

            /**
             * Свързване на събития за настройки за сигурност
             */
            bindSecuritySettingsEvents: function() {
                const self = this;

                // Форма за настройки за сигурност
                const securityForm = document.getElementById('security-settings-form');
                if (securityForm) {
                    securityForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveSecuritySettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-security-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveSecuritySettings();
                    });
                }

                // IP ограничения checkbox
                const ipRestrictionCheckbox = document.getElementById('ip_restriction_enabled');
                if (ipRestrictionCheckbox) {
                    ipRestrictionCheckbox.addEventListener('change', function() {
                        self.toggleIPRestrictionFields(this.checked);
                    });
                }

                // Бутон за тестване на IP ограничения
                const testIPButton = document.getElementById('test-ip-restriction');
                if (testIPButton) {
                    testIPButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testIPRestriction();
                    });
                }

                // Бутон за изчистване на неуспешни опити за вход
                const clearFailedLoginsButton = document.getElementById('clear-failed-logins');
                if (clearFailedLoginsButton) {
                    clearFailedLoginsButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.clearFailedLogins();
                    });
                }
            },

            /**
             * Обработка на click събития за security таб
             */
            handleSecurityClick: function(e) {
                const target = e.target;
                const self = this;

                if (target.id === 'save-security-settings') {
                    e.preventDefault();
                    self.saveSecuritySettings();
                } else if (target.id === 'test-ip-restriction') {
                    e.preventDefault();
                    self.testIPRestriction();
                } else if (target.id === 'clear-failed-logins') {
                    e.preventDefault();
                    self.clearFailedLogins();
                } else if (target.id === 'add-ip-address') {
                    e.preventDefault();
                    self.showAddIPModal();
                } else if (target.classList.contains('edit-ip')) {
                    e.preventDefault();
                    self.editIPAddress(target.dataset.ip);
                } else if (target.classList.contains('delete-ip')) {
                    e.preventDefault();
                    self.deleteIPAddress(target.dataset.ip);
                } else if (target.classList.contains('toggle-ip-status')) {
                    e.preventDefault();
                    self.toggleIPStatus(target.dataset.ip);
                }
            },

            /**
             * Обработка на form submit събития за security таб
             */
            handleSecurityFormSubmit: function(e) {
                e.preventDefault();
                const form = e.target;
                const self = this;

                if (form.id === 'security-settings-form') {
                    self.saveSecuritySettings();
                } else if (form.id === 'add-ip-form') {
                    self.submitAddIPForm(form);
                }
            },

            /**
             * Обработка на change събития за security таб
             */
            handleSecurityChange: function(e) {
                const target = e.target;
                const self = this;

                if (target.id === 'ip_restriction_enabled') {
                    self.toggleIPRestrictionFields(target.checked);
                }

                // Автоматично запазване при промяна на checkbox-и
                if (target.type === 'checkbox' && target.name && target.name.includes('setting')) {
                    self.autoSaveSettings();
                }
            },

            /**
             * Запазване на настройки за сигурност
             */
            saveSecuritySettings: function() {
                const self = this;
                const form = document.getElementById('security-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateSecurityForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-security-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.security_update || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateSecuritySettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving security settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за сигурност
             */
            validateSecurityForm: function(form) {
                const errors = [];

                // Валидация на минимална дължина на парола
                const minPasswordLength = form.querySelector('[name="security_password_min_length"]')?.value;
                if (minPasswordLength && (parseInt(minPasswordLength) < 6 || parseInt(minPasswordLength) > 32)) {
                    errors.push('Минималната дължина на паролата трябва да бъде между 6 и 32 символа');
                }

                // Валидация на максимални опити за вход
                const maxLoginAttempts = form.querySelector('[name="security_max_login_attempts"]')?.value;
                if (maxLoginAttempts && (parseInt(maxLoginAttempts) < 3 || parseInt(maxLoginAttempts) > 20)) {
                    errors.push('Максималните опити за вход трябва да бъдат между 3 и 20');
                }

                // Валидация на timeout на сесия
                const sessionTimeout = form.querySelector('[name="security_session_timeout"]')?.value;
                if (sessionTimeout && (parseInt(sessionTimeout) < 300 || parseInt(sessionTimeout) > 86400)) {
                    errors.push('Timeout на сесията трябва да бъде между 300 и 86400 секунди');
                }

                // Валидация на IP whitelist ако е активиран
                const ipRestrictionEnabled = form.querySelector('[name="security_ip_restriction_enabled"]')?.checked;
                const allowedIPs = form.querySelector('[name="security_allowed_ips"]')?.value;

                if (ipRestrictionEnabled && !allowedIPs) {
                    errors.push('IP ограниченията са активирани, но няма зададени разрешени IP адреси');
                } else if (allowedIPs) {
                    const ips = allowedIPs.split(',');
                    ips.forEach(ip => {
                        const trimmedIp = ip.trim();
                        if (trimmedIp && !this.isValidIP(trimmedIp)) {
                            errors.push(`Невалиден IP адрес: ${trimmedIp}`);
                        }
                    });
                }

                return errors;
            },

            /**
             * Проверка за валиден IP адрес
             */
            isValidIP: function(ip) {
                const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
                return ipRegex.test(ip);
            },

            /**
             * Toggle на IP restriction полета
             */
            toggleIPRestrictionFields: function(enabled) {
                const ipFields = document.querySelectorAll('.ip-restriction-field');
                ipFields.forEach(field => {
                    if (enabled) {
                        field.classList.remove('hidden');
                        field.style.display = 'block';
                    } else {
                        field.classList.add('hidden');
                        field.style.display = 'none';
                    }
                });
            },

            /**
             * Тестване на IP ограничения
             */
            testIPRestriction: function() {
                const self = this;
                
                fetch(self.settings.config.ajaxUrls.test_ip_restriction || '', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_token: self.settings.config.userToken
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'IP ограниченията работят правилно', 'success');
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при тестването', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing IP restriction:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                });
            },

            /**
             * Изчистване на неуспешни опити за вход
             */
            clearFailedLogins: function() {
                const self = this;
                
                if (!confirm('Сигурни ли сте, че искате да изчистите всички неуспешни опити за вход?')) {
                    return;
                }

                fetch(self.settings.config.ajaxUrls.clear_failed_logins || '', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_token: self.settings.config.userToken
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'Неуспешните опити за вход са изчистени', 'success');
                        
                        // Актуализиране на статистиките
                        const failedLoginsCount = document.getElementById('failed-logins-count');
                        if (failedLoginsCount) {
                            failedLoginsCount.textContent = '0';
                        }
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при изчистването', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error clearing failed logins:', error);
                    self.showSettingsNotification('Възникна грешка при изчистването', 'error');
                });
            },

            /**
             * Инициализиране на таблицата с IP адреси
             */
            initIPAddressesTable: function() {
                const self = this;
                const allowedIpsText = document.getElementById('allowed_ips_text');

                if (!allowedIpsText) {
                    return;
                }

                // Зареждане на съществуващите IP адреси
                const ipsText = allowedIpsText.value;
                if (ipsText) {
                    const ips = ipsText.split('\n').filter(ip => ip.trim());
                    ips.forEach(ip => {
                        self.addIPToTable(ip.trim(), 'Съществуващ IP', true);
                    });
                }

                self.updateIPTableVisibility();
                self.updateCurrentIPStatus();
            },

            /**
             * Показване на modal за добавяне на IP адрес
             */
            showAddIPModal: function() {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на IP адрес</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-ip-form" class="p-4 space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    IP Адрес <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="ip_address" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="*********** или ***********/24">
                                <p class="text-xs text-gray-500 mt-1">
                                    Поддържа отделни IP адреси и CIDR нотация
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Описание
                                </label>
                                <input type="text" name="description"
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="Описание на IP адреса">
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" name="enabled" id="ip-enabled" class="mr-2" checked>
                                <label for="ip-enabled" class="text-sm text-gray-700">Активен</label>
                            </div>
                        </form>
                        <div class="flex justify-end p-4 border-t space-x-2">
                            <button class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Отказ
                            </button>
                            <button id="save-ip-address" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                Добави
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Фокус на първото поле
                const firstInput = modal.querySelector('input[name="ip_address"]');
                if (firstInput) {
                    firstInput.focus();
                }
            },

            /**
             * Добавяне на IP адрес в таблицата
             */
            addIPToTable: function(ip, description = '', enabled = true) {
                const tableBody = document.getElementById('ip-addresses-table');
                if (!tableBody) {
                    return;
                }

                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                row.dataset.ip = ip;

                const statusClass = enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                const statusText = enabled ? 'Активен' : 'Неактивен';

                row.innerHTML = `
                    <td class="px-4 py-3 text-sm font-mono">${ip}</td>
                    <td class="px-4 py-3 text-sm text-gray-600">${description || '-'}</td>
                    <td class="px-4 py-3">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">
                            ${statusText}
                        </span>
                    </td>
                    <td class="px-4 py-3 text-right">
                        <div class="flex justify-end space-x-1">
                            <button class="toggle-ip-status p-1 text-gray-400 hover:text-primary"
                                    data-ip="${ip}" title="Промени статус">
                                <i class="ri-toggle-line"></i>
                            </button>
                            <button class="edit-ip p-1 text-gray-400 hover:text-primary"
                                    data-ip="${ip}" title="Редактирай">
                                <i class="ri-edit-line"></i>
                            </button>
                            <button class="delete-ip p-1 text-gray-400 hover:text-red-500"
                                    data-ip="${ip}" title="Изтрий">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                    </td>
                `;

                tableBody.appendChild(row);
                this.updateIPTableVisibility();
                this.updateAllowedIPsField();
                this.updateCurrentIPStatus();
            },

            /**
             * Актуализиране на видимостта на таблицата
             */
            updateIPTableVisibility: function() {
                const tableBody = document.getElementById('ip-addresses-table');
                const noIPsMessage = document.getElementById('no-ip-addresses');

                if (!tableBody || !noIPsMessage) {
                    return;
                }

                const hasIPs = tableBody.children.length > 0;

                if (hasIPs) {
                    noIPsMessage.classList.add('hidden');
                } else {
                    noIPsMessage.classList.remove('hidden');
                }
            },

            /**
             * Актуализиране на скритото поле с IP адресите
             */
            updateAllowedIPsField: function() {
                const tableBody = document.getElementById('ip-addresses-table');
                const allowedIpsField = document.getElementById('allowed_ips_text');

                if (!tableBody || !allowedIpsField) {
                    return;
                }

                const ips = [];
                Array.from(tableBody.children).forEach(row => {
                    const statusSpan = row.querySelector('span');
                    const isEnabled = statusSpan && statusSpan.textContent.trim() === 'Активен';

                    if (isEnabled) {
                        ips.push(row.dataset.ip);
                    }
                });

                allowedIpsField.value = ips.join('\n');
            },

            /**
             * Актуализиране на статуса на текущия IP
             */
            updateCurrentIPStatus: function() {
                const statusElement = document.getElementById('current-ip-status');
                const allowedIpsField = document.getElementById('allowed_ips_text');

                if (!statusElement || !allowedIpsField) {
                    return;
                }

                const currentIP = this.settings.config.currentIP || '';
                const allowedIPs = allowedIpsField.value.split('\n').map(ip => ip.trim()).filter(ip => ip);

                const isAllowed = allowedIPs.includes(currentIP);

                if (isAllowed) {
                    statusElement.innerHTML = '<span class="text-green-500 font-medium">✓ В списъка</span>';
                } else if (allowedIPs.length > 0) {
                    statusElement.innerHTML = '<span class="text-red-500 font-medium">⚠ Не е в списъка!</span>';
                } else {
                    statusElement.innerHTML = '';
                }
            },

            /**
             * Изтриване на IP адрес
             */
            deleteIPAddress: function(ip) {
                const self = this;

                if (!confirm('Сигурни ли сте, че искате да изтриете този IP адрес?')) {
                    return;
                }

                const row = document.querySelector(`tr[data-ip="${ip}"]`);
                if (row) {
                    row.remove();
                    self.updateIPTableVisibility();
                    self.updateAllowedIPsField();
                    self.updateCurrentIPStatus();
                    self.showSettingsNotification('IP адресът е изтрит', 'success');
                }
            },

            /**
             * Промяна на статуса на IP адрес
             */
            toggleIPStatus: function(ip) {
                const row = document.querySelector(`tr[data-ip="${ip}"]`);
                if (!row) {
                    return;
                }

                const statusSpan = row.querySelector('span');
                const isCurrentlyEnabled = statusSpan.textContent.trim() === 'Активен';

                if (isCurrentlyEnabled) {
                    statusSpan.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
                    statusSpan.textContent = 'Неактивен';
                } else {
                    statusSpan.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
                    statusSpan.textContent = 'Активен';
                }

                this.updateAllowedIPsField();
                this.updateCurrentIPStatus();
                this.showSettingsNotification('Статусът на IP адреса е променен', 'success');
            },

            /**
             * Редактиране на IP адрес
             */
            editIPAddress: function(ip) {
                // TODO: Implement edit functionality
                this.showSettingsNotification('Функционалността за редактиране ще бъде добавена скоро', 'info');
            },

            /**
             * Обработка на формата за добавяне на IP адрес
             */
            submitAddIPForm: function(form) {
                const self = this;
                const formData = new FormData(form);

                const ipAddress = formData.get('ip_address').trim();
                const description = formData.get('description').trim();
                const enabled = formData.get('enabled') === 'on';

                // Валидация на IP адреса
                if (!ipAddress) {
                    self.showSettingsNotification('Моля, въведете IP адрес', 'error');
                    return;
                }

                // Проверка дали IP адресът вече съществува
                const existingRow = document.querySelector(`tr[data-ip="${ipAddress}"]`);
                if (existingRow) {
                    self.showSettingsNotification('Този IP адрес вече съществува в списъка', 'error');
                    return;
                }

                // Добавяне на IP адреса в таблицата
                self.addIPToTable(ipAddress, description, enabled);

                // Затваряне на модала
                const modal = form.closest('.fixed.inset-0');
                if (modal) {
                    modal.remove();
                }

                self.showSettingsNotification('IP адресът е добавен успешно', 'success');
            },

            /**
             * Свързване на toggle за IP whitelist
             */
            bindIPWhitelistToggle: function() {
                const ipWhitelistToggle = document.getElementById('ip_whitelist_enabled');
                const ipWhitelistFields = document.getElementById('ip-whitelist-fields');

                if (ipWhitelistToggle && ipWhitelistFields) {
                    ipWhitelistToggle.addEventListener('change', function() {
                        if (this.checked) {
                            ipWhitelistFields.classList.remove('hidden');
                        } else {
                            ipWhitelistFields.classList.add('hidden');
                        }
                    });
                }
            }
        });
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initSecurityModule();
    });

})();
