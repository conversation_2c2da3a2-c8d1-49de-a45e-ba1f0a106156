/**
 * JavaScript модул за настройки на плащания
 * Следва BackendModule pattern на темата Rakla.bg
 * Разширява основния settings модул
 */
(function() {
    'use strict';

    // Функция за инициализация на payment модула
    function initPaymentModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModuleWithPayment();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул с функционалности за плащания
    function extendBackendModuleWithPayment() {
        Object.assign(BackendModule, {
            /**
             * Инициализиране на настройки за плащания
             */
            initPaymentSettings: function() {
                this.bindPaymentSettingsEvents();
                this.initPaymentMethodsSortable();
                this.logDev && this.logDev('Payment settings module initialized');
            },

            /**
             * Свързване на събития за настройки за плащания
             */
            bindPaymentSettingsEvents: function() {
                const self = this;

                // Форма за настройки за плащания
                const paymentForm = document.getElementById('payment-settings-form');
                if (paymentForm) {
                    paymentForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.savePaymentSettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-payment-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.savePaymentSettings();
                    });
                }

                // Toggle за методи на плащане
                const paymentToggles = document.querySelectorAll('.payment-method-toggle');
                paymentToggles.forEach(toggle => {
                    toggle.addEventListener('change', function() {
                        self.togglePaymentMethod(this.dataset.method, this.checked);
                    });
                });

                // Тест на методи на плащане
                const testButtons = document.querySelectorAll('.test-payment-method');
                testButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testPaymentMethod(this.dataset.method);
                    });
                });

                // Конфигуриране на методи на плащане
                const configButtons = document.querySelectorAll('.config-payment-method');
                configButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.configurePaymentMethod(this.dataset.method);
                    });
                });
            },

            /**
             * Обработка на click събития за payment таб
             */
            handlePaymentClick: function(e) {
                const target = e.target;
                const self = this;

                if (target.id === 'save-payment-settings') {
                    e.preventDefault();
                    self.savePaymentSettings();
                } else if (target.classList.contains('test-payment-method')) {
                    e.preventDefault();
                    self.testPaymentMethod(target.dataset.method);
                } else if (target.classList.contains('config-payment-method')) {
                    e.preventDefault();
                    self.configurePaymentMethod(target.dataset.method);
                }
            },

            /**
             * Обработка на form submit събития за payment таб
             */
            handlePaymentFormSubmit: function(e) {
                e.preventDefault();
                const form = e.target;
                const self = this;

                if (form.id === 'payment-settings-form') {
                    self.savePaymentSettings();
                } else if (form.id === 'payment-method-config-form') {
                    self.savePaymentMethodConfig(form);
                }
            },

            /**
             * Обработка на change събития за payment таб
             */
            handlePaymentChange: function(e) {
                const target = e.target;
                const self = this;

                if (target.classList.contains('payment-method-toggle')) {
                    self.togglePaymentMethod(target.dataset.method, target.checked);
                }

                // Автоматично запазване при промяна на checkbox-и
                if (target.type === 'checkbox' && target.name && target.name.includes('setting')) {
                    self.autoSaveSettings();
                }
            },

            /**
             * Запазване на настройки за плащания
             */
            savePaymentSettings: function() {
                const self = this;
                const form = document.getElementById('payment-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validatePaymentForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-payment-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.payment_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updatePaymentSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving payment settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за плащания
             */
            validatePaymentForm: function(form) {
                const errors = [];

                // Валидация на валута по подразбиране
                const defaultCurrency = form.querySelector('[name="payment_default_currency"]')?.value;
                if (!defaultCurrency) {
                    errors.push('Валутата по подразбиране е задължителна');
                }

                // Валидация на минимална сума за поръчка
                const minOrderAmount = form.querySelector('[name="payment_min_order_amount"]')?.value;
                if (minOrderAmount && parseFloat(minOrderAmount) < 0) {
                    errors.push('Минималната сума за поръчка не може да бъде отрицателна');
                }

                // Валидация на максимална сума за поръчка
                const maxOrderAmount = form.querySelector('[name="payment_max_order_amount"]')?.value;
                if (maxOrderAmount && parseFloat(maxOrderAmount) < 0) {
                    errors.push('Максималната сума за поръчка не може да бъде отрицателна');
                }

                if (minOrderAmount && maxOrderAmount && parseFloat(minOrderAmount) > parseFloat(maxOrderAmount)) {
                    errors.push('Минималната сума не може да бъде по-голяма от максималната');
                }

                // Валидация на активни методи на плащане
                const activePaymentMethods = form.querySelectorAll('.payment-method-toggle:checked');
                if (activePaymentMethods.length === 0) {
                    errors.push('Трябва да има поне един активен метод на плащане');
                }

                return errors;
            },

            /**
             * Toggle на метод на плащане
             */
            togglePaymentMethod: function(methodCode, enabled) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_code', methodCode);
                formData.append('enabled', enabled ? '1' : '0');

                fetch(self.settings.config.ajaxUrls.toggle_payment_method || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'Методът на плащане е актуализиран', 'success');

                        // Актуализиране на визуалното състояние
                        const methodCard = document.querySelector(`[data-method="${methodCode}"]`);
                        if (methodCard) {
                            const card = methodCard.closest('.payment-method-card');
                            if (enabled) {
                                card.classList.remove('opacity-50');
                                card.classList.add('border-green-200', 'bg-green-50');
                            } else {
                                card.classList.add('opacity-50');
                                card.classList.remove('border-green-200', 'bg-green-50');
                            }
                        }
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при актуализирането', 'error');

                        // Връщане на toggle-а в предишното състояние
                        const toggle = document.querySelector(`[data-method="${methodCode}"].payment-method-toggle`);
                        if (toggle) {
                            toggle.checked = !enabled;
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error toggling payment method:', error);
                    self.showSettingsNotification('Възникна грешка при промяната', 'error');
                });
            },

            /**
             * Тестване на метод на плащане
             */
            testPaymentMethod: function(methodCode) {
                const self = this;
                const testButton = document.querySelector(`[data-method="${methodCode}"].test-payment-method`);

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_code', methodCode);

                fetch(self.settings.config.ajaxUrls.test_payment_method || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'Методът на плащане работи правилно', 'success');
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при тестването', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing payment method:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-play-line mr-2"></i>Тест';
                    }
                });
            },

            /**
             * Конфигуриране на метод на плащане
             */
            configurePaymentMethod: function(methodCode) {
                const self = this;

                // Зареждане на конфигурацията за метода
                const getConfigUrl = self.settings.config.ajaxUrls.get_payment_method_config ||
                                    window.location.pathname + '?route=setting/payment/get_config&user_token=' + self.settings.config.userToken + '&method=' + methodCode;

                fetch(getConfigUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.config) {
                        self.showPaymentMethodConfigModal(methodCode, data.config);
                    } else {
                        self.showSettingsNotification('Грешка при зареждане на конфигурацията', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error loading payment method config:', error);
                    self.showSettingsNotification('Грешка при зареждане на конфигурацията', 'error');
                });
            },

            /**
             * Инициализиране на sortable за payment методи
             */
            initPaymentMethodsSortable: function() {
                const paymentMethodsList = document.getElementById('payment-methods-list');
                if (paymentMethodsList && typeof Sortable !== 'undefined') {
                    new Sortable(paymentMethodsList, {
                        animation: 150,
                        ghostClass: 'sortable-ghost',
                        onEnd: (evt) => {
                            this.reorderPaymentMethods();
                        }
                    });
                }
            },

            /**
             * Преподреждане на payment методи
             */
            reorderPaymentMethods: function() {
                const self = this;
                const paymentMethodsList = document.getElementById('payment-methods-list');

                if (!paymentMethodsList) return;

                const methodCodes = [];
                const methodRows = paymentMethodsList.querySelectorAll('[data-method]');

                methodRows.forEach((row, index) => {
                    const methodCode = row.dataset.method;
                    if (methodCode) {
                        methodCodes.push({
                            code: methodCode,
                            sort_order: index + 1
                        });
                    }
                });

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_order', JSON.stringify(methodCodes));

                fetch(self.settings.config.ajaxUrls.reorder_payment_methods || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification('Редът на методите е актуализиран', 'success');
                    } else {
                        self.showSettingsNotification('Грешка при актуализиране на реда', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error reordering payment methods:', error);
                    self.showSettingsNotification('Грешка при актуализиране на реда', 'error');
                });
            }
        });
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initPaymentModule();
    });

})();
